<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Answer;
use App\Models\Exam;
use App\Models\ExamDetail;
use App\Models\ExamResult;
use App\Models\ExamTemporaryAnswer;
use App\Models\Question;
use App\Models\QuestionMaterial;
use App\Models\Student;
use App\Models\StudentClass;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExamParticipantsExport;
use Dompdf\Dompdf;
use Dompdf\Options;
use Carbon\Carbon;

class ExamApiController extends Controller
{
    /**
     * Get all exams for teacher
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();

            // Role-based filtering: teachers see only their own exams, admins see all exams
            if ($user->role === 'teacher') {
                $exams = Exam::with(['questionmaterial', 'class', 'teacher'])
                    ->where('teacher_id', $user->id)
                    ->orderBy('created_at', 'desc')
                    ->get();
            } else {
                // Admin can see all exams
                $exams = Exam::with(['questionmaterial', 'class', 'teacher'])
                    ->orderBy('created_at', 'desc')
                    ->get();
            }

            $examData = $exams->map(function ($exam) {
                return [
                    'id' => $exam->id,
                    'name' => $exam->name,
                    'startdate' => $exam->startdate,
                    'enddate' => $exam->enddate,
                    'starttime' => $exam->starttime,
                    'endtime' => $exam->endtime,
                    'duration' => $exam->duration,
                    'token' => $exam->token,
                    'kkm' => $exam->kkm,
                    'amountquestion' => $exam->amountquestion,
                    'trials' => $exam->trials,
                    'questionmaterial' => [
                        'id' => $exam->questionmaterial->id ?? null,
                        'name' => $exam->questionmaterial->name ?? null,
                    ],
                    'class' => [
                        'id' => $exam->class->id ?? null,
                        'name' => $exam->class->name ?? null,
                    ],
                    'teacher' => [
                        'id' => $exam->teacher->id ?? null,
                        'name' => $exam->teacher->name ?? null,
                    ],
                    'created_at' => $exam->created_at,
                    'updated_at' => $exam->updated_at,
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Exams retrieved successfully',
                'data' => [
                    'exams' => $examData
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve exams',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get specific exam
     */
    public function show(Request $request, $id)
    {
        try {
            $exam = Exam::with(['questionmaterial', 'class', 'examDetails.question'])->findOrFail($id);

            // Role-based access control: teachers can only view their own exams
            $user = $request->user();
            if ($user->role === 'teacher' && $exam->teacher_id != $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. You can only view your own exams.'
                ], 403);
            }

            $examData = [
                'id' => $exam->id,
                'name' => $exam->name,
                'startdate' => $exam->startdate,
                'enddate' => $exam->enddate,
                'starttime' => $exam->starttime,
                'endtime' => $exam->endtime,
                'duration' => $exam->duration,
                'token' => $exam->token,
                'kkm' => $exam->kkm,
                'amountquestion' => $exam->amountquestion,
                'trials' => $exam->trials,
                'questionmaterial' => [
                    'id' => $exam->questionmaterial->id ?? null,
                    'name' => $exam->questionmaterial->name ?? null,
                ],
                'class' => [
                    'id' => $exam->class->id ?? null,
                    'name' => $exam->class->name ?? null,
                ],
                'questions' => $exam->examDetails->map(function ($detail) {
                    return [
                        'id' => $detail->question->id ?? null,
                        'question' => $detail->question->question ?? null,
                        'type' => $detail->question->type ?? null,
                    ];
                }),
                'created_at' => $exam->created_at,
                'updated_at' => $exam->updated_at,
            ];

            return response()->json([
                'success' => true,
                'message' => 'Exam retrieved successfully',
                'data' => [
                    'exam' => $examData
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Exam not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Create new exam
     */
    public function store(Request $request)
    {
        try {
            // Check if schedule is provided (non-empty dates/times)
            $hasSchedule = !empty($request->startdate) && !empty($request->enddate) &&
                !empty($request->starttime) && !empty($request->endtime);

            $validationRules = [
                'name' => 'required|string|max:255',
                'kkm' => 'required|numeric|min:0|max:100',
                'classid' => 'required|exists:class,id',
                'questionmaterialid' => 'required|exists:questionmaterial,id',
                'trials' => 'required|integer|min:1',
                'question_ids' => 'required|array|min:1',
                'question_ids.*' => 'exists:question,id',
                'show_score' => 'nullable|boolean',
            ];

            if ($hasSchedule) {
                // If schedule is provided, validate date/time fields
                $validationRules['startdate'] = 'required|date';
                $validationRules['enddate'] = 'required|date|after_or_equal:startdate';
                $validationRules['starttime'] = 'required|date_format:H:i';
                $validationRules['endtime'] = 'required|date_format:H:i';
            } else {
                // If no schedule, require duration
                $validationRules['duration'] = 'required|integer|min:1';
            }

            $request->validate($validationRules);

            // Validate class exists
            $class = StudentClass::where('id', $request->classid)
                ->first();

            if (!$class) {
                return response()->json([
                    'success' => false,
                    'message' => 'Selected class not found or has been deleted'
                ], 422);
            }

            // Validate question material exists
            $questionMaterial = QuestionMaterial::find($request->questionmaterialid);
            if (!$questionMaterial) {
                return response()->json([
                    'success' => false,
                    'message' => 'Selected question material not found'
                ], 422);
            }

            // Validate all questions exist
            $existingQuestions = Question::whereIn('id', $request->question_ids)->count();
            if ($existingQuestions !== count($request->question_ids)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Some selected questions do not exist'
                ], 422);
            }

            // Handle different scenarios based on schedule availability
            $duration = 0;
            $startdate = null;
            $enddate = null;
            $starttime = null;
            $endtime = null;

            if ($hasSchedule) {
                // Schedule is provided - validate and calculate duration
                $startdate = $request->startdate;
                $enddate = $request->enddate;
                $starttime = $request->starttime;
                $endtime = $request->endtime;

                // Validasi konflik jadwal
                if (Exam::hasScheduleConflict(
                    $request->classid,
                    $startdate,
                    $enddate,
                    $starttime,
                    $endtime
                )) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Jadwal ujian bentrok dengan ujian yang sudah ada untuk kelas ini'
                    ], 422);
                }

                // Validate time logic
                if ($startdate === $enddate && $starttime > $endtime) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Waktu mulai tidak boleh lebih lambat dari waktu akhir',
                        'errors' => ['starttime' => ['Waktu mulai tidak boleh lebih lambat dari waktu akhir']]
                    ], 422);
                }

                // Calculate duration automatically
                $startDateTime = new \DateTime($startdate . ' ' . $starttime);
                $endDateTime = new \DateTime($enddate . ' ' . $endtime);
                $interval = $startDateTime->diff($endDateTime);
                $duration = ($interval->days * 24 * 60) + ($interval->h * 60) + $interval->i;

                // Validate that end time is after start time
                if ($duration <= 0) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Waktu selesai harus setelah waktu mulai'
                    ], 422);
                }
            } else {
                // Manual duration - use provided duration
                $duration = $request->duration;
            }

            // Generate token
            $token = $this->generateToken();

            $exam = Exam::create([
                'name' => $request->name,
                'startdate' => $startdate,
                'enddate' => $enddate,
                'starttime' => $starttime,
                'endtime' => $endtime,
                'duration' => $duration,
                'token' => $token,
                'kkm' => $request->kkm,
                'classid' => $request->classid,
                'amountquestion' => count($request->question_ids),
                'questionmaterialid' => $request->questionmaterialid,
                'trials' => $request->trials,
                'teacher_id' => $request->user()->id,
                'show_score' => $request->has('show_score') ? (bool)$request->show_score : true,
            ]);

            // Create exam details
            foreach ($request->question_ids as $questionId) {
                ExamDetail::create([
                    'examid' => $exam->id,
                    'questionid' => $questionId,
                    'classid' => $request->classid,
                    'studentid' => null,
                ]);
            }

            // Simple response to avoid JSON serialization issues
            return response()->json([
                'success' => true,
                'message' => 'Exam created successfully',
                'data' => [
                    'exam' => [
                        'id' => $exam->id,
                        'name' => $exam->name,
                        'token' => $exam->token,
                    ]
                ]
            ], 201);
        } catch (ValidationException $e) {
            // Create specific error message based on validation errors
            $errorMessages = [];
            foreach ($e->errors() as $messages) {
                $errorMessages = array_merge($errorMessages, $messages);
            }

            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create exam',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update exam
     */
    public function update(Request $request, $id)
    {
        try {
            $exam = Exam::findOrFail($id);

            // Role-based access control: teachers can only update their own exams
            $user = $request->user();
            if ($user->role === 'teacher' && $exam->teacher_id != $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. You can only update your own exams.'
                ], 403);
            }

            // Check if schedule is provided (non-empty dates/times)
            $hasSchedule = !empty($request->startdate) && !empty($request->enddate) &&
                !empty($request->starttime) && !empty($request->endtime);

            $validationRules = [
                'name' => 'required|string|max:255',
                'kkm' => 'required|numeric|min:0|max:100',
                'classid' => 'required|exists:class,id',
                'questionmaterialid' => 'required|exists:questionmaterial,id',
                'trials' => 'required|integer|min:1',
                'question_ids' => 'required|array|min:1',
                'question_ids.*' => 'exists:question,id',
                'show_score' => 'nullable|boolean',
            ];

            // Conditional validation based on whether schedule is provided
            if ($hasSchedule) {
                // If schedule is provided, validate date/time fields
                $validationRules['startdate'] = 'required|date';
                $validationRules['enddate'] = 'required|date|after_or_equal:startdate';
                $validationRules['starttime'] = 'required|date_format:H:i';
                $validationRules['endtime'] = 'required|date_format:H:i';
                $validationRules['duration'] = 'required|integer|min:1';
            } else {
                // If no schedule, require manual duration
                $validationRules['duration'] = 'required|integer|min:1';
                $validationRules['startdate'] = 'nullable';
                $validationRules['enddate'] = 'nullable';
                $validationRules['starttime'] = 'nullable';
                $validationRules['endtime'] = 'nullable';
            }

            $request->validate($validationRules);

            // Validasi konflik jadwal hanya jika exam memiliki schedule (kecualikan ujian yang sedang diedit)
            if ($hasSchedule && Exam::hasScheduleConflict(
                $request->classid,
                $request->startdate,
                $request->enddate,
                $request->starttime,
                $request->endtime,
                $id // Exclude current exam
            )) {
                $conflictingExams = Exam::getConflictingExams(
                    $request->classid,
                    $request->startdate,
                    $request->enddate,
                    $request->starttime,
                    $request->endtime,
                    $id
                );

                $conflictDetails = $conflictingExams->map(function ($conflictExam) {
                    return [
                        'name' => $conflictExam->name,
                        'startdate' => $conflictExam->startdate,
                        'enddate' => $conflictExam->enddate,
                        'starttime' => $conflictExam->starttime,
                        'endtime' => $conflictExam->endtime,
                    ];
                });

                return response()->json([
                    'success' => false,
                    'message' => 'Jadwal ujian bentrok dengan ujian yang sudah ada untuk kelas ini',
                    'conflicting_exams' => $conflictDetails
                ], 422);
            }

            // Handle different scenarios based on whether schedule is provided
            $duration = 0;
            $startdate = null;
            $enddate = null;
            $starttime = null;
            $endtime = null;

            if ($hasSchedule) {
                // Schedule is provided - validate and calculate duration
                $startdate = $request->startdate;
                $enddate = $request->enddate;
                $starttime = $request->starttime;
                $endtime = $request->endtime;

                // Validate time logic
                if ($startdate === $enddate && $starttime > $endtime) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Waktu mulai tidak boleh lebih lambat dari waktu akhir',
                        'errors' => ['starttime' => ['Waktu mulai tidak boleh lebih lambat dari waktu akhir']]
                    ], 422);
                }

                // Calculate duration automatically
                $startDateTime = new \DateTime($startdate . ' ' . $starttime);
                $endDateTime = new \DateTime($enddate . ' ' . $endtime);
                $interval = $startDateTime->diff($endDateTime);
                $duration = ($interval->days * 24 * 60) + ($interval->h * 60) + $interval->i;

                // Validate that end time is after start time
                if ($duration <= 0) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Waktu selesai harus setelah waktu mulai'
                    ], 422);
                }
            } else {
                // No schedule - use manual duration
                $duration = $request->duration;
            }

            // Update exam
            $exam->update([
                'name' => $request->name,
                'startdate' => $startdate,
                'enddate' => $enddate,
                'starttime' => $starttime,
                'endtime' => $endtime,
                'duration' => $duration,
                'kkm' => $request->kkm,
                'classid' => $request->classid,
                'amountquestion' => count($request->question_ids),
                'questionmaterialid' => $request->questionmaterialid,
                'trials' => $request->trials,
                'teacher_id' => $request->user()->id,
                'show_score' => $request->has('show_score') ? (bool)$request->show_score : true,
            ]);

            // Delete existing exam details
            ExamDetail::where('examid', $exam->id)->delete();

            // Create new exam details
            foreach ($request->question_ids as $questionId) {
                ExamDetail::create([
                    'examid' => $exam->id,
                    'questionid' => $questionId,
                    'classid' => $request->classid,
                    'studentid' => null,
                ]);
            }

            // Load relationships for response
            $exam->load(['questionmaterial', 'class']);

            $examData = [
                'id' => $exam->id,
                'name' => $exam->name,
                'startdate' => $exam->startdate,
                'enddate' => $exam->enddate,
                'starttime' => $exam->starttime,
                'endtime' => $exam->endtime,
                'duration' => $exam->duration,
                'token' => $exam->token,
                'kkm' => $exam->kkm,
                'amountquestion' => $exam->amountquestion,
                'trials' => $exam->trials,
                'questionmaterial' => [
                    'id' => $exam->questionmaterial->id,
                    'name' => $exam->questionmaterial->name,
                ],
                'class' => [
                    'id' => $exam->class->id,
                    'name' => $exam->class->name,
                ],
                'created_at' => $exam->created_at,
                'updated_at' => $exam->updated_at,
            ];

            return response()->json([
                'success' => true,
                'message' => 'Exam updated successfully',
                'data' => [
                    'exam' => $examData
                ]
            ]);
        } catch (ValidationException $e) {
            // Create specific error message based on validation errors
            $errorMessages = [];
            foreach ($e->errors() as $messages) {
                $errorMessages = array_merge($errorMessages, $messages);
            }

            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update exam',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete exam
     */
    public function destroy(Request $request, $id)
    {
        try {
            $exam = Exam::findOrFail($id);

            // Role-based access control: teachers can only delete their own exams
            $user = $request->user();
            if ($user->role === 'teacher' && $exam->teacher_id != $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. You can only delete your own exams.'
                ], 403);
            }

            // Delete exam details first
            ExamDetail::where('examid', $exam->id)->delete();

            // Delete exam
            $exam->delete();

            return response()->json([
                'success' => true,
                'message' => 'Exam deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete exam',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get dropdown data for exam form
     */
    public function getFormData()
    {
        try {
            $classes = StudentClass::select('id', 'name')
                ->orderBy('name', 'asc')
                ->get();
            $questionMaterials = QuestionMaterial::select('id', 'name')->orderBy('name', 'asc')->get();

            return response()->json([
                'success' => true,
                'message' => 'Form data retrieved successfully',
                'data' => [
                    'classes' => $classes,
                    'questionMaterials' => $questionMaterials
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve form data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get questions by material ID
     */
    public function getQuestionsByMaterial($materialId)
    {
        try {
            // Get question material info
            $questionMaterial = QuestionMaterial::find($materialId);
            if (!$questionMaterial) {
                return response()->json([
                    'success' => false,
                    'message' => 'Question material not found'
                ], 404);
            }

            // Get questions with answers
            $questions = Question::where('questionmaterialid', $materialId)
                ->with(['answers' => function ($query) {
                    // For students taking exam, don't show which answer is correct
                    $query->select('id', 'questionid', 'answer');
                }])
                ->select('id', 'question', 'type', 'img')
                ->get()
                ->groupBy('type');

            return response()->json([
                'success' => true,
                'message' => 'Questions retrieved successfully',
                'data' => [
                    'questions' => $questions,
                    'pg_total_score' => $questionMaterial->pg_total_score,
                    'uraian_total_score' => $questionMaterial->uraian_total_score,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve questions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get exam schedules for student
     */
    public function getStudentExams(Request $request)
    {
        try {
            $user = $request->user();

            // Check if user is a student
            if ($user->role !== 'student') {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. Only students can access this endpoint.'
                ], 403);
            }

            // Load student data with class
            $user->load(['student.class']);

            if (!$user->student || !$user->student->classid) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student class not found.'
                ], 404);
            }

            // Get exams for student's class, ordered by newest first
            $exams = Exam::with(['questionmaterial', 'class'])
                ->where('classid', $user->student->classid)
                ->orderBy('created_at', 'desc')
                ->get();

            $examData = $exams->map(function ($exam) {
                return [
                    'id' => $exam->id,
                    'name' => $exam->name,
                    'startdate' => $exam->startdate,
                    'enddate' => $exam->enddate,
                    'starttime' => $exam->starttime,
                    'endtime' => $exam->endtime,
                    'duration' => $exam->duration,
                    'token' => $exam->token,
                    'kkm' => $exam->kkm,
                    'amountquestion' => $exam->amountquestion,
                    'trials' => $exam->trials,
                    'questionmaterial' => $exam->questionmaterial ? [
                        'id' => $exam->questionmaterial->id,
                        'name' => $exam->questionmaterial->name,
                        'description' => $exam->questionmaterial->description,
                    ] : null,
                    'class' => $exam->class ? [
                        'id' => $exam->class->id,
                        'name' => $exam->class->name,
                        'level' => $exam->class->level,
                    ] : null,
                    'created_at' => $exam->created_at?->format('Y-m-d H:i:s'),
                    'updated_at' => $exam->updated_at?->format('Y-m-d H:i:s'),
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Student exams retrieved successfully',
                'data' => [
                    'exams' => $examData
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve student exams',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check exam time status
     */
    public function checkExamTime($examId)
    {
        try {
            $exam = Exam::find($examId);

            if (!$exam) {
                return response()->json([
                    'success' => false,
                    'message' => 'Ujian tidak ditemukan'
                ], 404);
            }

            // Check if exam has schedule (startdate, enddate, starttime, endtime are not null)
            $hasSchedule = !empty($exam->startdate) && !empty($exam->enddate) &&
                !empty($exam->starttime) && !empty($exam->endtime);

            if (!$hasSchedule) {
                // Exam without schedule - always available
                return response()->json([
                    'success' => true,
                    'message' => 'Ujian tersedia (tanpa jadwal waktu)',
                    'data' => [
                        'exam' => $exam,
                        'status' => 'active',
                        'start_time' => null,
                        'end_time' => null,
                        'current_time' => now()->format('Y-m-d H:i:s'),
                        'can_start' => true,
                        'has_schedule' => false
                    ]
                ], 200);
            }

            $now = now();

            // Ensure proper timezone handling for scheduled exams
            $startDateTime = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $exam->startdate . ' ' . $exam->starttime, config('app.timezone'));
            $endDateTime = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $exam->enddate . ' ' . $exam->endtime, config('app.timezone'));

            $status = 'active';
            $message = 'Ujian sedang berlangsung';

            if ($now->isBefore($startDateTime)) {
                $status = 'not_started';
                $message = 'Ujian belum dimulai. Akan dimulai pada ' . $startDateTime->format('d/m/Y H:i');
            } elseif ($now->isAfter($endDateTime)) {
                $status = 'ended';
                $message = 'Waktu ujian sudah berakhir. Berakhir pada ' . $endDateTime->format('d/m/Y H:i');
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => [
                    'exam' => $exam,
                    'status' => $status,
                    'start_time' => $startDateTime->format('Y-m-d H:i:s'),
                    'end_time' => $endDateTime->format('Y-m-d H:i:s'),
                    'current_time' => $now->format('Y-m-d H:i:s'),
                    'can_start' => $status === 'active',
                    'has_schedule' => true
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check exam time',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get exam questions with randomized order for students
     */
    public function getExamQuestions($examId)
    {
        try {
            $exam = Exam::with(['examDetails.question.answers'])->find($examId);

            if (!$exam) {
                return response()->json([
                    'success' => false,
                    'message' => 'Exam not found'
                ], 404);
            }

            // Get questions from exam details (maintains original order for teacher)
            $questions = $exam->examDetails->map(function ($detail) {
                $question = $detail->question;
                if (!$question) return null;

                return [
                    'id' => $question->id,
                    'question' => $question->question,
                    'type' => $question->type,
                    'img' => $question->img,
                    'original_order' => $detail->id, // Store original order for teacher reference
                    'answers' => $question->answers->map(function ($answer) {
                        return [
                            'id' => $answer->id,
                            'answer' => $answer->answer,
                            // Don't include is_correct for students
                        ];
                    })
                ];
            })->filter()->values();

            // Randomize questions for students
            $randomizedQuestions = $questions->shuffle();

            return response()->json([
                'success' => true,
                'message' => 'Exam questions retrieved successfully',
                'data' => [
                    'exam' => [
                        'id' => $exam->id,
                        'name' => $exam->name,
                        'duration' => $exam->duration,
                        'amountquestion' => $exam->amountquestion,
                    ],
                    'questions' => $randomizedQuestions
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve exam questions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate exam token and check if exam is still active
     */
    public function validateToken(Request $request)
    {
        try {
            $request->validate([
                'token' => 'required|string',
                'exam_id' => 'required|integer',
            ]);

            $exam = Exam::where('token', $request->token)->first();

            if (!$exam) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token ujian tidak valid'
                ], 404);
            }

            // Check if token matches the specific exam
            if ($exam->id != $request->exam_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token salah untuk ujian ini'
                ], 400);
            }

            // Check student's exam attempts and scores
            $user = Auth::user();
            if (!$user->studentid) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student ID not found for this user'
                ], 400);
            }
            $studentId = $user->studentid;

            $examResults = ExamResult::where('examid', $exam->id)
                ->where('studentid', $studentId)
                ->orderBy('score', 'desc')
                ->get();

            $attemptCount = $examResults->count();
            $bestScore = $examResults->first()?->score ?? 0;

            // Get exam settings from database
            $kkm = $exam->kkm ?? 75; // Default KKM 75 if not set
            $maxAttempts = $exam->trials ?? 1; // Use trials from exam table

            // Check if student can still take the exam
            $canRetake = false;
            $message = '';

            if ($attemptCount == 0) {
                // First attempt
                $canRetake = true;
            } elseif ($attemptCount >= $maxAttempts) {
                // Reached max attempts
                $canRetake = false;
                $message = "Anda telah mencapai batas maksimal percobaan ($maxAttempts kali)";
            } elseif ($bestScore >= $kkm) {
                // Already passed KKM
                $canRetake = false;
                $message = "Anda sudah mencapai nilai KKM ($kkm). Skor terbaik: $bestScore";
            } else {
                // Can retake (score below KKM and attempts remaining)
                $canRetake = true;
            }

            if (!$canRetake) {
                return response()->json([
                    'success' => false,
                    'message' => $message,
                    'data' => [
                        'exam' => $exam,
                        'result' => [
                            'best_score' => $bestScore,
                            'attempt_count' => $attemptCount,
                            'max_attempts' => $maxAttempts,
                            'kkm' => $kkm
                        ]
                    ]
                ], 400);
            }

            // Check if exam has schedule (startdate, enddate, starttime, endtime are not null)
            $hasSchedule = !empty($exam->startdate) && !empty($exam->enddate) &&
                !empty($exam->starttime) && !empty($exam->endtime);

            if ($hasSchedule) {
                // Check if exam is still active (date and time validation) for scheduled exams
                $now = now();
                $startDateTime = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $exam->startdate . ' ' . $exam->starttime, config('app.timezone'));
                $endDateTime = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $exam->enddate . ' ' . $exam->endtime, config('app.timezone'));

                if ($now->isBefore($startDateTime)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Ujian belum dimulai. Ujian akan dimulai pada ' . $startDateTime->format('d/m/Y H:i'),
                        'data' => [
                            'exam' => $exam,
                            'start_time' => $startDateTime->format('Y-m-d H:i:s'),
                            'current_time' => $now->format('Y-m-d H:i:s'),
                            'status' => 'not_started'
                        ]
                    ], 400);
                }

                if ($now->isAfter($endDateTime)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Waktu ujian sudah berakhir. Ujian berakhir pada ' . $endDateTime->format('d/m/Y H:i'),
                        'data' => [
                            'exam' => $exam,
                            'end_time' => $endDateTime->format('Y-m-d H:i:s'),
                            'current_time' => $now->format('Y-m-d H:i:s'),
                            'status' => 'ended'
                        ]
                    ], 400);
                }
            }
            // For exams without schedule, skip time validation - they are always available

            return response()->json([
                'success' => true,
                'message' => 'Token valid, ujian dapat dimulai',
                'data' => [
                    'exam' => $exam,
                    'current_attempt' => $attemptCount + 1,
                    'max_attempts' => (int) $maxAttempts,
                    'kkm' => (int) $kkm
                ]
            ], 200);
        } catch (ValidationException $e) {
            // Create specific error message based on validation errors
            $errorMessages = [];
            foreach ($e->errors() as $messages) {
                $errorMessages = array_merge($errorMessages, $messages);
            }

            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to validate token',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get exam result for student (best attempt)
     */
    public function getExamResult($examId)
    {
        try {
            $user = Auth::user();
            if (!$user->studentid) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student ID not found for this user'
                ], 400);
            }
            $studentId = $user->studentid;

            // Get the best score from all attempts
            $examResult = ExamResult::with('exam')
                ->where('examid', $examId)
                ->where('studentid', $studentId)
                ->orderBy('score', 'desc')
                ->orderBy('submitted_at', 'desc')
                ->first();

            if (!$examResult) {
                return response()->json([
                    'success' => false,
                    'message' => 'Hasil ujian tidak ditemukan'
                ], 404);
            }

            // Get attempt count
            $attemptCount = ExamResult::where('examid', $examId)
                ->where('studentid', $studentId)
                ->count();

            // Check if exam contains essay questions
            $hasEssayQuestions = Question::where('questionmaterialid', $examResult->exam->questionmaterialid)
                ->where('type', 'esai')
                ->exists();

            // Only show temporary score message if exam has essay questions AND status is not 'graded'
            $showTemporaryScore = $hasEssayQuestions && $examResult->status !== 'graded';

            return response()->json([
                'success' => true,
                'message' => 'Hasil ujian berhasil diambil',
                'data' => [
                    'result' => [
                        'score' => $examResult->score,
                        'submitted_at' => $examResult->submitted_at,
                        'status' => $examResult->status,
                        'attempt' => $examResult->attempt,
                        'total_attempts' => $attemptCount
                    ],
                    'exam' => $examResult->exam,
                    'kkm' => (int) ($examResult->exam->kkm ?? 75),
                    'max_attempts' => (int) ($examResult->exam->trials ?? 1),
                    'has_essay_questions' => $showTemporaryScore
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get exam result',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Save temporary answer
     */
    public function saveTemporaryAnswer(Request $request)
    {
        try {
            $request->validate([
                'exam_id' => 'required|integer',
                'question_id' => 'required|integer',
                'question_type' => 'required|string',
                'selected_answer' => 'nullable|string',
                'text_answer' => 'nullable|string',
                'image_path' => 'nullable|string', // Keep for backward compatibility
                'image_paths' => 'nullable|array', // New field for multiple images
                'image_paths.*' => 'nullable|string',
                'has_attachment' => 'nullable|boolean',
            ]);

            $user = Auth::user();
            if (!$user->studentid) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student ID not found for this user'
                ], 400);
            }
            $studentId = $user->studentid;

            // Get current attempt number
            $currentAttempt = ExamResult::where('examid', $request->exam_id)
                ->where('studentid', $studentId)
                ->count() + 1;

            // Save or update temporary answer
            ExamTemporaryAnswer::updateOrCreate(
                [
                    'examid' => $request->exam_id,
                    'studentid' => $studentId,
                    'questionid' => $request->question_id,
                    'attempt' => $currentAttempt
                ],
                [
                    'question_type' => $request->question_type,
                    'selected_answer' => $request->selected_answer,
                    'text_answer' => $request->text_answer,
                    'image_path' => $request->image_path, // Keep for backward compatibility
                    'image_paths' => $request->image_paths ? json_encode($request->image_paths) : null, // Store as JSON string
                    'has_attachment' => $request->has_attachment ?? false
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'Jawaban sementara berhasil disimpan'
            ], 200);
        } catch (ValidationException $e) {
            // Create specific error message based on validation errors
            $errorMessages = [];
            foreach ($e->errors() as $messages) {
                $errorMessages = array_merge($errorMessages, $messages);
            }

            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save temporary answer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get temporary answers for current attempt
     */
    public function getTemporaryAnswers($examId)
    {
        try {
            $user = Auth::user();
            if (!$user->studentid) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student ID not found for this user'
                ], 400);
            }
            $studentId = $user->studentid;

            // Get current attempt number
            $currentAttempt = ExamResult::where('examid', $examId)
                ->where('studentid', $studentId)
                ->count() + 1;

            $temporaryAnswers = ExamTemporaryAnswer::where('examid', $examId)
                ->where('studentid', $studentId)
                ->where('attempt', $currentAttempt)
                ->get()
                ->map(function ($answer) {
                    // Decode image_paths from JSON string back to array
                    if ($answer->image_paths && is_string($answer->image_paths)) {
                        $answer->image_paths = json_decode($answer->image_paths, true);
                    }
                    return $answer;
                });

            return response()->json([
                'success' => true,
                'message' => 'Jawaban sementara berhasil diambil',
                'data' => [
                    'answers' => $temporaryAnswers,
                    'attempt' => $currentAttempt
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get temporary answers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload exam answer image (single)
     */
    public function uploadAnswerImage(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'file' => 'required|image|mimes:jpeg,jpg,png|max:5120', // 5MB max
            ]);

            if ($validator->fails()) {
                // Create specific error message based on validation errors
                $errorMessages = [];
                foreach ($validator->errors()->all() as $message) {
                    $errorMessages[] = $message;
                }

                return response()->json([
                    'success' => false,
                    'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                    'errors' => $validator->errors()
                ], 422);
            }

            $file = $request->file('file');
            $filename = hash('sha256', time() . '_' . uniqid()) . '.' . $file->getClientOriginalExtension();

            // Use move() method instead of storeAs to avoid finfo dependency
            $uploadPath = public_path('storage/uploads/images/answers/');
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }
            $file->move($uploadPath, $filename);

            return response()->json([
                'success' => true,
                'message' => 'Answer image uploaded successfully',
                'data' => [
                    'filename' => $filename,
                    'url' => '/storage/uploads/images/answers/' . $filename
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload answer image',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload multiple exam answer images
     */
    public function uploadAnswerImages(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'files' => 'required|array|max:5', // Maximum 5 images
                'files.*' => 'required|image|mimes:jpeg,jpg,png|max:5120', // 5MB max per image
            ]);

            if ($validator->fails()) {
                // Create specific error message based on validation errors
                $errorMessages = [];
                foreach ($validator->errors()->all() as $message) {
                    $errorMessages[] = $message;
                }

                return response()->json([
                    'success' => false,
                    'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                    'errors' => $validator->errors()
                ], 422);
            }

            $uploadedFiles = [];
            $uploadPath = public_path('storage/uploads/images/answers/');
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            foreach ($request->file('files') as $file) {
                $filename = hash('sha256', time() . '_' . uniqid() . '_' . $file->getClientOriginalName()) . '.' . $file->getClientOriginalExtension();
                $file->move($uploadPath, $filename);

                $uploadedFiles[] = [
                    'filename' => $filename,
                    'url' => '/storage/uploads/images/answers/' . $filename
                ];
            }

            return response()->json([
                'success' => true,
                'message' => 'Answer images uploaded successfully',
                'data' => [
                    'files' => $uploadedFiles,
                    'count' => count($uploadedFiles)
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload answer images',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Submit exam answers
     */
    public function submitAnswers(Request $request, $id)
    {
        try {
            $exam = Exam::find($id);

            if (!$exam) {
                return response()->json([
                    'success' => false,
                    'message' => 'Exam not found'
                ], 404);
            }

            // Log the request for debugging
            Log::info('Exam submission attempt', [
                'exam_id' => $id,
                'user_id' => Auth::id(),
                'answers_count' => count($request->input('answers', [])),
                'request_data' => $request->all()
            ]);

            $request->validate([
                'answers' => 'required|array',
                'answers.*.question_id' => 'required|integer',
                'answers.*.question_type' => 'required|string',
                'answers.*.selected_answer' => 'nullable|string',
                'answers.*.text_answer' => 'nullable|string',
                'answers.*.image_path' => 'nullable|string',
                'answers.*.image_paths' => 'nullable|array', // Add validation for multiple images
                'answers.*.image_paths.*' => 'nullable|string',
                'answers.*.has_attachment' => 'nullable|boolean',
                'submitted_at' => 'required|string',
            ]);

            // Get the student ID from the authenticated user
            $user = Auth::user();
            if (!$user->studentid) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student ID not found for this user'
                ], 400);
            }
            $studentId = $user->studentid;

            // Get current attempt number
            $currentAttempt = ExamResult::where('examid', $id)
                ->where('studentid', $studentId)
                ->count() + 1;

            // Get exam with question material to calculate score
            $exam = Exam::with('questionmaterial')->find($id);
            if (!$exam) {
                return response()->json([
                    'success' => false,
                    'message' => 'Exam not found'
                ], 404);
            }

            // Get all questions for this exam to calculate score per question
            $allQuestions = Question::where('questionmaterialid', $exam->questionmaterialid)->get();
            $questionsByType = $allQuestions->groupBy('type');

            $multipleChoiceCount = $questionsByType->get('pilihan_ganda', collect())->count();
            $shortAnswerCount = $questionsByType->get('uraian_singkat', collect())->count();

            // Get score settings from question material
            $pgTotalScore = $exam->questionmaterial->pg_total_score ?? ($multipleChoiceCount * 10);
            $uraianTotalScore = $exam->questionmaterial->uraian_total_score ?? ($shortAnswerCount * 15);

            // Calculate score per question (total skor : total soal)
            $scorePerMultipleChoice = $multipleChoiceCount > 0 ? $pgTotalScore / $multipleChoiceCount : 0;
            $scorePerShortAnswer = $shortAnswerCount > 0 ? $uraianTotalScore / $shortAnswerCount : 0;

            // Calculate score based on answers and check for essay questions
            $totalScore = 0;
            $correctAnswers = 0;
            $totalQuestions = count($request->answers);
            $hasEssayQuestions = false;

            foreach ($request->answers as $answerData) {
                $question = Question::find($answerData['question_id']);
                if (!$question) continue;

                $isCorrect = false;

                if ($answerData['question_type'] === 'pilihan_ganda') {
                    // Check if selected answer is correct
                    $correctAnswer = $question->answers()
                        ->where('is_correct', 1)
                        ->first();

                    if ($correctAnswer && isset($answerData['selected_answer'])) {
                        // Compare with answer ID, not answer text
                        $isCorrect = $correctAnswer->id == $answerData['selected_answer'];
                    }

                    if ($isCorrect) {
                        $totalScore += $scorePerMultipleChoice; // Use calculated score per question
                        $correctAnswers++;
                    }
                } elseif ($answerData['question_type'] === 'uraian_singkat') {
                    // For short answer, compare with correct answer
                    $correctAnswer = $question->answers()
                        ->where('is_correct', 1)
                        ->first();

                    if ($correctAnswer && isset($answerData['text_answer'])) {
                        // Normalize both answers for comparison (case-insensitive, trim whitespace)
                        $studentAnswer = trim(strtolower($answerData['text_answer']));
                        $correctAnswerText = trim(strtolower($correctAnswer->answer));

                        // Check if answers match exactly
                        $isCorrect = $studentAnswer === $correctAnswerText;

                        if ($isCorrect) {
                            $totalScore += $scorePerShortAnswer; // Use calculated score per question
                            $correctAnswers++;
                        }
                        // If incorrect, no score is added (0 points)
                    }
                } elseif ($answerData['question_type'] === 'esai') {
                    // Mark that this exam has essay questions
                    $hasEssayQuestions = true;
                    // For essay, score will be given manually later
                    // For now, just record the answer
                }
            }

            // Process answers to ensure image_paths are properly encoded
            $processedAnswers = [];
            foreach ($request->answers as $answer) {
                $processedAnswer = $answer;

                // Encode image_paths as JSON string if it's an array
                if (isset($answer['image_paths']) && is_array($answer['image_paths'])) {
                    $processedAnswer['image_paths'] = json_encode($answer['image_paths']);
                }

                $processedAnswers[] = $processedAnswer;
            }

            // Create exam result with attempt number
            // Set status based on whether exam has essay questions
            // If has essay questions: 'completed' (needs manual review)
            // If no essay questions: 'graded' (fully auto-scored)
            ExamResult::create([
                'examid' => $id,
                'studentid' => $studentId,
                'attempt' => $currentAttempt,
                'score' => $totalScore,
                'submitted_at' => $request->submitted_at,
                'status' => $hasEssayQuestions ? 'completed' : 'graded',
                'answers' => $processedAnswers
            ]);

            // Clear temporary answers for this attempt
            ExamTemporaryAnswer::where('examid', $id)
                ->where('studentid', $studentId)
                ->where('attempt', $currentAttempt)
                ->delete();

            return response()->json([
                'success' => true,
                'message' => 'Exam submitted successfully',
                'data' => [
                    'score' => $totalScore,
                    'correct_answers' => $correctAnswers,
                    'total_questions' => $totalQuestions
                ]
            ], 200);
        } catch (ValidationException $e) {
            Log::error('Exam submission validation failed', [
                'exam_id' => $id,
                'user_id' => Auth::id(),
                'validation_errors' => $e->errors(),
                'request_data' => $request->all()
            ]);

            // Create specific error message based on validation errors
            $errorMessages = [];
            foreach ($e->errors() as $messages) {
                $errorMessages = array_merge($errorMessages, $messages);
            }

            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Exam submission failed', [
                'exam_id' => $id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to submit exam',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get exam participants (students) with their results
     */
    public function getExamParticipants($examId)
    {
        try {
            $exam = Exam::with(['questionmaterial', 'class.students'])->find($examId);

            if (!$exam) {
                return response()->json([
                    'success' => false,
                    'message' => 'Exam not found'
                ], 404);
            }

            if (!$exam->class) {
                return response()->json([
                    'success' => false,
                    'message' => 'Exam class not found'
                ], 404);
            }

            // Get students with their exam results and progress
            $students = $exam->class->students->map(function ($student) use ($exam) {
                // Get all exam results for this student
                $examResults = ExamResult::where('examid', $exam->id)
                    ->where('studentid', $student->id)
                    ->orderBy('score', 'desc')
                    ->get();

                // Get best score, attempt count, and actual status
                $bestResult = $examResults->first();
                $bestScore = $bestResult?->score ?? null;
                $attemptCount = $examResults->count();
                $actualStatus = $bestResult?->status ?? null;

                // Calculate progress from temporary answers
                $totalQuestions = Question::where('questionmaterialid', $exam->questionmaterialid)->count();

                $answeredQuestions = ExamTemporaryAnswer::where('examid', $exam->id)
                    ->where('studentid', $student->id)
                    ->where('attempt', $attemptCount + 1) // Current attempt
                    ->distinct('questionid')
                    ->count();

                $progress = $totalQuestions > 0 ? round(($answeredQuestions / $totalQuestions) * 100) : 0;

                // Determine status based on actual exam result status
                $status = 'not_started';
                if ($actualStatus) {
                    // Use the actual status from exam result (completed or graded)
                    $status = $actualStatus;
                } elseif ($progress > 0) {
                    $status = 'in_progress';
                }

                return [
                    'id' => $student->id,
                    'name' => $student->name,
                    'nis' => $student->nis,
                    'email' => $student->user->email ?? null,
                    'best_score' => $bestScore,
                    'attempt_count' => $attemptCount,
                    'progress' => $progress,
                    'total_questions' => $totalQuestions,
                    'answered_questions' => $answeredQuestions,
                    'status' => $status,
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Exam participants retrieved successfully',
                'data' => [
                    'exam' => [
                        'id' => $exam->id,
                        'name' => $exam->name,
                        'class' => [
                            'id' => $exam->class->id,
                            'name' => $exam->class->name,
                        ],
                        'questionmaterial' => [
                            'id' => $exam->questionmaterial->id,
                            'name' => $exam->questionmaterial->name,
                        ],
                    ],
                    'students' => $students
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve exam participants',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get student exam detail with answers
     */
    public function getStudentExamDetail($examId, $studentId)
    {
        try {
            $exam = Exam::with(['questionmaterial', 'class'])->findOrFail($examId);
            $student = Student::with('user')->findOrFail($studentId);

            // Get student's exam result
            $examResult = ExamResult::where('examid', $examId)
                ->where('studentid', $studentId)
                ->orderBy('score', 'desc')
                ->first();

            if (!$examResult) {
                return response()->json([
                    'success' => false,
                    'message' => 'Exam result not found'
                ], 404);
            }

            // Get questions for this exam in original order from exam_details
            $examDetails = ExamDetail::where('examid', $examId)
                ->with(['question.answers'])
                ->orderBy('id', 'asc') // Maintain original order
                ->get();

            // Parse student answers from JSON
            $studentAnswers = $examResult->answers ?? [];

            // Group questions by type for better organization, maintaining original order
            $questionsByType = [];
            foreach ($examDetails as $detail) {
                if ($detail->question) {
                    $type = $detail->question->type;
                    if (!isset($questionsByType[$type])) {
                        $questionsByType[$type] = [];
                    }

                    // Find student answer for this question
                    $studentAnswer = collect($studentAnswers)->firstWhere('question_id', $detail->question->id);

                    // Decode image_paths if it's a JSON string
                    if ($studentAnswer && isset($studentAnswer['image_paths']) && is_string($studentAnswer['image_paths'])) {
                        $studentAnswer['image_paths'] = json_decode($studentAnswer['image_paths'], true);
                    }

                    $questionData = [
                        'id' => $detail->question->id,
                        'question' => $detail->question->question,
                        'type' => $detail->question->type,
                        'img' => $detail->question->img,
                        'answers' => $detail->question->answers->map(function ($answer) {
                            return [
                                'id' => $answer->id,
                                'answer' => $answer->answer,
                                'is_correct' => $answer->is_correct,
                                'score' => $answer->score,
                            ];
                        }),
                        'student_answer' => $studentAnswer,
                    ];

                    $questionsByType[$type][] = $questionData;
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Student exam detail retrieved successfully',
                'data' => [
                    'exam' => [
                        'id' => $exam->id,
                        'name' => $exam->name,
                        'class' => [
                            'id' => $exam->class->id,
                            'name' => $exam->class->name,
                        ],
                        'questionmaterial' => [
                            'id' => $exam->questionmaterial->id,
                            'name' => $exam->questionmaterial->name,
                        ],
                    ],
                    'student' => [
                        'id' => $student->id,
                        'name' => $student->name,
                        'nis' => $student->nis,
                        'email' => $student->user->email ?? null,
                    ],
                    'exam_result' => [
                        'id' => $examResult->id,
                        'score' => $examResult->score,
                        'status' => $examResult->status,
                        'submitted_at' => $examResult->submitted_at,
                    ],
                    'questions_by_type' => $questionsByType,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve student exam detail',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update student essay score
     */
    public function updateStudentEssayScore(Request $request, $examId, $studentId)
    {
        try {
            $request->validate([
                'question_id' => 'required|integer',
                'score' => 'required|numeric|min:0',
            ]);

            // Get the question to check maximum score
            $question = Question::with('answers')->find($request->question_id);
            if (!$question || $question->type !== 'esai') {
                return response()->json([
                    'success' => false,
                    'message' => 'Essay question not found'
                ], 404);
            }

            // Get maximum score for this essay question
            $essayAnswer = $question->answers->where('is_correct', 1)->first();
            $maxScore = $essayAnswer->score ?? 100;

            // Validate score against maximum
            if ($request->score > $maxScore) {
                return response()->json([
                    'success' => false,
                    'message' => "Score cannot exceed {$maxScore} points"
                ], 422);
            }

            $examResult = ExamResult::where('examid', $examId)
                ->where('studentid', $studentId)
                ->orderBy('score', 'desc')
                ->first();

            if (!$examResult) {
                return response()->json([
                    'success' => false,
                    'message' => 'Exam result not found'
                ], 404);
            }

            // Update the score for specific essay question
            $answers = $examResult->answers ?? [];

            // Find and update the specific answer
            foreach ($answers as &$answer) {
                if ($answer['question_id'] == $request->question_id && $answer['question_type'] == 'esai') {
                    $answer['essay_score'] = $request->score;
                    break;
                }
            }

            // Recalculate total score using the same method as ScheduleExamController
            $totalScore = $this->calculateTotalScore($examId, $answers);

            // Update exam result
            $examResult->update([
                'answers' => $answers,
                'score' => $totalScore,
                'status' => 'graded'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Essay score updated successfully',
                'data' => [
                    'new_total_score' => $totalScore
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update essay score',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate total score including essay scores (same as ScheduleExamController)
     */
    private function calculateTotalScore($examId, $answers)
    {
        $totalScore = 0;

        // Get exam to determine total possible score
        $exam = Exam::with('questionmaterial')->find($examId);
        if (!$exam) {
            return 0;
        }

        // Get all questions for this exam
        $questions = Question::where('questionmaterialid', $exam->questionmaterialid)->get();
        $questionsByType = $questions->groupBy('type');

        // Calculate score based on question types
        $multipleChoiceCount = $questionsByType->get('pilihan_ganda', collect())->count();
        $shortAnswerCount = $questionsByType->get('uraian_singkat', collect())->count();

        // Get score settings from question material
        $pgTotalScore = $exam->questionmaterial->pg_total_score ?? ($multipleChoiceCount * 10);
        $uraianTotalScore = $exam->questionmaterial->uraian_total_score ?? ($shortAnswerCount * 15);

        // Calculate score per question (total skor : total soal)
        $scorePerMultipleChoice = $multipleChoiceCount > 0 ? $pgTotalScore / $multipleChoiceCount : 0;
        $scorePerShortAnswer = $shortAnswerCount > 0 ? $uraianTotalScore / $shortAnswerCount : 0;

        foreach ($answers as $answer) {
            if ($answer['question_type'] === 'pilihan_ganda') {
                // Get the question and check if answer is correct
                $question = Question::find($answer['question_id']);
                if ($question) {
                    $correctAnswer = Answer::where('questionid', $question->id)
                        ->where('is_correct', 1)
                        ->first();
                    if ($correctAnswer && $correctAnswer->id == $answer['selected_answer']) {
                        $totalScore += $scorePerMultipleChoice;
                    }
                }
            } elseif ($answer['question_type'] === 'uraian_singkat') {
                // For short answer, check if answer is correct
                $question = Question::find($answer['question_id']);
                if ($question) {
                    $correctAnswer = Answer::where('questionid', $question->id)
                        ->where('is_correct', 1)
                        ->first();

                    if ($correctAnswer && isset($answer['text_answer'])) {
                        // Normalize both answers for comparison (case-insensitive, trim whitespace)
                        $studentAnswer = trim(strtolower($answer['text_answer']));
                        $correctAnswerText = trim(strtolower($correctAnswer->answer));

                        // Check if answers match exactly
                        if ($studentAnswer === $correctAnswerText) {
                            $totalScore += $scorePerShortAnswer;
                        }
                        // If incorrect, no score is added (0 points)
                    }
                }
            } elseif ($answer['question_type'] === 'esai') {
                // Use manually graded score directly (no conversion needed)
                $essayScore = $answer['essay_score'] ?? 0;
                $totalScore += $essayScore;
            }
        }

        return round($totalScore, 2);
    }

    /**
     * Generate unique token for exam (3 characters: letters and numbers)
     */
    private function generateToken()
    {
        do {
            $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            $token = '';
            for ($i = 0; $i < 3; $i++) {
                $token .= $characters[random_int(0, strlen($characters) - 1)];
            }
        } while (Exam::where('token', $token)->exists());

        return $token;
    }

    /**
     * Export exam participants to Excel (for Flutter app)
     */
    public function exportExcel($examId)
    {
        try {
            $exam = Exam::with(['questionmaterial', 'class.students'])->findOrFail($examId);

            $fileName = 'Laporan_Ujian_' . str_replace(' ', '_', $exam->name) . '_' . Carbon::now()->format('Y-m-d_H-i-s') . '.xlsx';

            return Excel::download(new ExamParticipantsExport($examId), $fileName);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengekspor data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export exam participants to PDF (for Flutter app)
     */
    public function exportPdf($examId)
    {
        try {
            $exam = Exam::with(['questionmaterial', 'class.students'])->findOrFail($examId);

            // Get exam data using the same logic as ExportController
            $examData = $this->getExamDataForExport($examId);

            // Generate PDF content
            $html = view('exports.exam-participants-pdf', [
                'exam' => $exam,
                'examData' => $examData,
                'generatedAt' => Carbon::now()->format('d/m/Y H:i:s')
            ])->render();

            // Configure Dompdf
            $options = new Options();
            $options->set('defaultFont', 'Arial');
            $options->set('isRemoteEnabled', true);
            $options->set('isHtml5ParserEnabled', true);

            $dompdf = new Dompdf($options);
            $dompdf->loadHtml($html);
            $dompdf->setPaper('A4', 'landscape');
            $dompdf->render();

            $fileName = 'Laporan_Ujian_' . str_replace(' ', '_', $exam->name) . '_' . Carbon::now()->format('Y-m-d_H-i-s') . '.pdf';

            return $dompdf->stream($fileName);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengekspor PDF: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get formatted exam data for export (helper method)
     */
    private function getExamDataForExport($examId)
    {
        $exam = Exam::with(['questionmaterial', 'class.students'])->findOrFail($examId);

        // Get all questions for this exam grouped by type
        $examDetails = ExamDetail::where('examid', $examId)
            ->with(['question.answers'])
            ->orderBy('id', 'asc')
            ->get();

        $questionsByType = [
            'pilihan_ganda' => [],
            'uraian_singkat' => [],
            'esai' => []
        ];

        foreach ($examDetails as $detail) {
            if ($detail->question) {
                $type = $detail->question->type;
                $questionsByType[$type][] = $detail->question;
            }
        }

        // Get students with their results
        $students = $exam->class->students->map(function ($student) use ($exam, $questionsByType) {
            // Get best exam result for this student
            $examResult = ExamResult::where('examid', $exam->id)
                ->where('studentid', $student->id)
                ->orderBy('score', 'desc')
                ->first();

            $studentData = [
                'id' => $student->id,
                'name' => $student->name,
                'nis' => $student->nis,
                'scores' => [
                    'pilihan_ganda' => [],
                    'uraian_singkat' => [],
                    'esai' => []
                ],
                'total_score' => $examResult ? $examResult->score : 0,
                'status' => $examResult ? $examResult->status : 'not_taken'
            ];

            if ($examResult && $examResult->answers) {
                $answers = $examResult->answers;

                // Process answers by question type
                foreach ($answers as $answer) {
                    $questionType = $answer['question_type'] ?? '';
                    $questionId = $answer['question_id'] ?? 0;

                    if (isset($questionsByType[$questionType])) {
                        $question = collect($questionsByType[$questionType])->firstWhere('id', $questionId);
                        if ($question) {
                            $score = 0;

                            if ($questionType === 'pilihan_ganda') {
                                // Check if selected answer is correct
                                $isCorrect = false;
                                if (isset($answer['selected_answer'])) {
                                    $correctAnswer = Answer::where('questionid', $questionId)
                                        ->where('is_correct', 1)
                                        ->first();
                                    if ($correctAnswer) {
                                        $isCorrect = $correctAnswer->id == $answer['selected_answer'];
                                    }
                                }
                                $score = $isCorrect ? $this->getQuestionScoreForExport($exam, $questionType) : 0;
                            } elseif ($questionType === 'uraian_singkat') {
                                // Check if text answer is correct
                                $isCorrect = false;
                                if (isset($answer['text_answer'])) {
                                    $correctAnswer = Answer::where('questionid', $questionId)
                                        ->where('is_correct', 1)
                                        ->first();
                                    if ($correctAnswer) {
                                        $studentAnswer = trim(strtolower($answer['text_answer']));
                                        $correctAnswerText = trim(strtolower($correctAnswer->answer));
                                        $isCorrect = $studentAnswer === $correctAnswerText;
                                    }
                                }
                                $score = $isCorrect ? $this->getQuestionScoreForExport($exam, $questionType) : 0;
                            } elseif ($questionType === 'esai') {
                                $score = $answer['essay_score'] ?? 0;
                            }

                            $studentData['scores'][$questionType][] = [
                                'question_id' => $questionId,
                                'score' => $score,
                                'max_score' => $this->getQuestionMaxScoreForExport($exam, $questionType, $questionId)
                            ];
                        }
                    }
                }
            }

            // Recalculate total score based on individual scores
            $totalCalculatedScore = 0;
            foreach ($studentData['scores'] as $questionType => $scores) {
                foreach ($scores as $scoreData) {
                    $totalCalculatedScore += $scoreData['score'];
                }
            }

            // Use the recalculated score for consistency
            $studentData['total_score'] = $totalCalculatedScore;

            return $studentData;
        });

        return [
            'exam' => $exam,
            'questions_by_type' => $questionsByType,
            'students' => $students,
            'statistics' => $this->calculateStatisticsForExport($students, $questionsByType)
        ];
    }

    /**
     * Get question score based on type (helper method)
     */
    private function getQuestionScoreForExport($exam, $type)
    {
        $material = $exam->questionmaterial;

        switch ($type) {
            case 'pilihan_ganda':
                $totalQuestions = Question::where('questionmaterialid', $exam->questionmaterialid)
                    ->where('type', 'pilihan_ganda')->count();
                if ($totalQuestions > 0) {
                    // Use saved score or default to 10 points per question
                    $totalScore = $material->pg_total_score ?? ($totalQuestions * 10);
                    return $totalScore / $totalQuestions;
                }
                return 0;

            case 'uraian_singkat':
                $totalQuestions = Question::where('questionmaterialid', $exam->questionmaterialid)
                    ->where('type', 'uraian_singkat')->count();
                if ($totalQuestions > 0) {
                    // Use saved score or default to 15 points per question
                    $totalScore = $material->uraian_total_score ?? ($totalQuestions * 15);
                    return $totalScore / $totalQuestions;
                }
                return 0;

            case 'esai':
                return 0;

            default:
                return 0;
        }
    }

    /**
     * Get maximum score for a specific question (helper method)
     */
    private function getQuestionMaxScoreForExport($exam, $type, $questionId)
    {
        if ($type === 'esai') {
            $answer = Answer::where('questionid', $questionId)->first();
            return $answer ? $answer->score : 0;
        }

        return $this->getQuestionScoreForExport($exam, $type);
    }

    /**
     * Calculate statistics for the exam (helper method)
     */
    private function calculateStatisticsForExport($students, $questionsByType)
    {
        $stats = [
            'total_students' => $students->count(),
            'completed_students' => $students->where('status', '!=', 'not_taken')->count(),
            'average_score' => 0,
            'highest_score' => 0,
            'lowest_score' => 0,
            'by_type' => []
        ];

        $completedStudents = $students->where('status', '!=', 'not_taken');

        if ($completedStudents->count() > 0) {
            $scores = $completedStudents->pluck('total_score');
            $stats['average_score'] = round($scores->avg(), 2);
            $stats['highest_score'] = $scores->max();
            $stats['lowest_score'] = $scores->min();
        }

        // Calculate statistics by question type
        foreach (['pilihan_ganda', 'uraian_singkat', 'esai'] as $type) {
            if (!empty($questionsByType[$type])) {
                $typeScores = [];
                foreach ($completedStudents as $student) {
                    $typeScore = collect($student['scores'][$type])->sum('score');
                    $typeScores[] = $typeScore;
                }

                $stats['by_type'][$type] = [
                    'average' => count($typeScores) > 0 ? round(array_sum($typeScores) / count($typeScores), 2) : 0,
                    'highest' => count($typeScores) > 0 ? max($typeScores) : 0,
                    'lowest' => count($typeScores) > 0 ? min($typeScores) : 0
                ];
            }
        }

        return $stats;
    }

    /**
     * Admin-specific methods for exam management
     */

    /**
     * Get all exams for admin (can see all exams)
     */
    public function adminIndex(Request $request)
    {
        try {
            $exams = Exam::with(['questionmaterial', 'class', 'teacher'])
                ->orderBy('created_at', 'desc')
                ->get();

            $formattedExams = $exams->map(function ($exam) {
                return [
                    'id' => $exam->id,
                    'name' => $exam->name,
                    'startdate' => $exam->startdate,
                    'enddate' => $exam->enddate,
                    'starttime' => $exam->starttime,
                    'endtime' => $exam->endtime,
                    'duration' => $exam->duration,
                    'token' => $exam->token,
                    'kkm' => $exam->kkm,
                    'amountquestion' => $exam->amountquestion,
                    'trials' => $exam->trials,
                    'questionmaterial' => $exam->questionmaterial ? [
                        'id' => $exam->questionmaterial->id,
                        'name' => $exam->questionmaterial->name,
                    ] : null,
                    'class' => $exam->class ? [
                        'id' => $exam->class->id,
                        'name' => $exam->class->name,
                    ] : null,
                    'teacher' => $exam->teacher ? [
                        'id' => $exam->teacher->id,
                        'name' => $exam->teacher->name,
                    ] : null,
                    'created_at' => $exam->created_at,
                    'updated_at' => $exam->updated_at,
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Exams retrieved successfully',
                'data' => [
                    'exams' => $formattedExams
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve exams',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get form data for admin exam creation (includes teachers list)
     */
    public function adminGetFormData(Request $request)
    {
        try {
            $classes = StudentClass::select('id', 'name')->orderBy('name')->get();
            $questionMaterials = QuestionMaterial::select('id', 'name')->orderBy('name')->get();
            $teachers = \App\Models\User::where('role', 'teacher')->select('id', 'name')->orderBy('name')->get();

            return response()->json([
                'success' => true,
                'message' => 'Form data retrieved successfully',
                'data' => [
                    'classes' => $classes,
                    'question_materials' => $questionMaterials,
                    'teachers' => $teachers
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve form data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show specific exam for admin
     */
    public function adminShow($id)
    {
        try {
            $exam = Exam::with(['questionmaterial', 'class', 'teacher', 'examDetails.question.answers'])
                ->findOrFail($id);

            $formattedExam = [
                'id' => $exam->id,
                'name' => $exam->name,
                'startdate' => $exam->startdate,
                'enddate' => $exam->enddate,
                'starttime' => $exam->starttime,
                'endtime' => $exam->endtime,
                'duration' => $exam->duration,
                'token' => $exam->token,
                'kkm' => $exam->kkm,
                'amountquestion' => $exam->amountquestion,
                'trials' => $exam->trials,
                'questionmaterial' => $exam->questionmaterial ? [
                    'id' => $exam->questionmaterial->id,
                    'name' => $exam->questionmaterial->name,
                ] : null,
                'class' => $exam->class ? [
                    'id' => $exam->class->id,
                    'name' => $exam->class->name,
                ] : null,
                'teacher' => $exam->teacher ? [
                    'id' => $exam->teacher->id,
                    'name' => $exam->teacher->name,
                ] : null,
                'questions' => $exam->examDetails->map(function ($detail) {
                    return [
                        'id' => $detail->question->id,
                        'question' => $detail->question->question,
                        'type' => $detail->question->type,
                        'answers' => $detail->question->answers->map(function ($answer) {
                            return [
                                'id' => $answer->id,
                                'answer' => $answer->answer,
                                'is_correct' => $answer->is_correct,
                            ];
                        }),
                    ];
                }),
                'created_at' => $exam->created_at,
                'updated_at' => $exam->updated_at,
            ];

            return response()->json([
                'success' => true,
                'message' => 'Exam retrieved successfully',
                'data' => [
                    'exam' => $formattedExam
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve exam',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create new exam (admin version with teacher selection)
     */
    public function adminStore(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'startdate' => 'required|date',
                'enddate' => 'required|date|after_or_equal:startdate',
                'starttime' => 'required|date_format:H:i',
                'endtime' => 'required|date_format:H:i',
                'kkm' => 'required|numeric|min:0|max:100',
                'classid' => 'required|exists:class,id',
                'questionmaterialid' => 'required|exists:questionmaterial,id',
                'trials' => 'required|integer|min:1',
                'question_ids' => 'required|array|min:1',
                'question_ids.*' => 'exists:question,id',
                'teacher_id' => 'required|exists:users,id',
            ]);

            // Validate class exists
            $class = StudentClass::where('id', $request->classid)->first();
            if (!$class) {
                return response()->json([
                    'success' => false,
                    'message' => 'Kelas tidak ditemukan.'
                ], 404);
            }

            // Validate teacher exists and has teacher role
            $teacher = \App\Models\User::where('id', $request->teacher_id)
                ->where('role', 'teacher')
                ->first();
            if (!$teacher) {
                return response()->json([
                    'success' => false,
                    'message' => 'Guru tidak ditemukan atau tidak valid.'
                ], 404);
            }

            // Validate question material exists
            $questionMaterial = QuestionMaterial::where('id', $request->questionmaterialid)->first();
            if (!$questionMaterial) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bank soal tidak ditemukan.'
                ], 404);
            }

            // Validate all questions exist and belong to the question material
            $questions = Question::whereIn('id', $request->question_ids)
                ->where('questionmaterialid', $request->questionmaterialid)
                ->get();

            if ($questions->count() !== count($request->question_ids)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Beberapa soal tidak ditemukan atau tidak sesuai dengan bank soal yang dipilih.'
                ], 422);
            }

            // Calculate duration
            $startDateTime = new \DateTime($request->startdate . ' ' . $request->starttime);
            $endDateTime = new \DateTime($request->enddate . ' ' . $request->endtime);
            $interval = $startDateTime->diff($endDateTime);
            $duration = ($interval->days * 24 * 60) + ($interval->h * 60) + $interval->i;

            if ($duration <= 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Waktu selesai harus setelah waktu mulai.'
                ], 422);
            }

            // Check for schedule conflicts
            if (Exam::hasScheduleConflict(
                $request->classid,
                $request->startdate,
                $request->enddate,
                $request->starttime,
                $request->endtime
            )) {
                $conflictingExams = Exam::getConflictingExams(
                    $request->classid,
                    $request->startdate,
                    $request->enddate,
                    $request->starttime,
                    $request->endtime
                );

                $conflictDetails = $conflictingExams->map(function ($conflictExam) {
                    return [
                        'name' => $conflictExam->name,
                        'startdate' => $conflictExam->startdate,
                        'enddate' => $conflictExam->enddate,
                        'starttime' => $conflictExam->starttime,
                        'endtime' => $conflictExam->endtime,
                    ];
                });

                return response()->json([
                    'success' => false,
                    'message' => 'Jadwal ujian bentrok dengan ujian yang sudah ada untuk kelas ini.',
                    'conflicts' => $conflictDetails
                ], 422);
            }

            // Generate unique token (3 characters: letters and numbers)
            do {
                $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                $token = '';
                for ($i = 0; $i < 3; $i++) {
                    $token .= $characters[random_int(0, strlen($characters) - 1)];
                }
            } while (Exam::where('token', $token)->exists());

            // Create exam
            $exam = Exam::create([
                'name' => $request->name,
                'startdate' => $request->startdate,
                'enddate' => $request->enddate,
                'starttime' => $request->starttime,
                'endtime' => $request->endtime,
                'duration' => $duration,
                'token' => $token,
                'kkm' => $request->kkm,
                'classid' => $request->classid,
                'amountquestion' => count($request->question_ids),
                'questionmaterialid' => $request->questionmaterialid,
                'trials' => $request->trials,
                'teacher_id' => $request->teacher_id,
            ]);

            // Create exam details
            foreach ($request->question_ids as $questionId) {
                ExamDetail::create([
                    'examid' => $exam->id,
                    'questionid' => $questionId,
                    'classid' => $request->classid,
                    'studentid' => null,
                ]);
            }

            // Load relationships for response
            $exam->load(['questionmaterial', 'class', 'teacher']);

            return response()->json([
                'success' => true,
                'message' => 'Ujian berhasil dibuat.',
                'data' => [
                    'exam' => [
                        'id' => $exam->id,
                        'name' => $exam->name,
                        'startdate' => $exam->startdate,
                        'enddate' => $exam->enddate,
                        'starttime' => $exam->starttime,
                        'endtime' => $exam->endtime,
                        'duration' => $exam->duration,
                        'token' => $exam->token,
                        'kkm' => $exam->kkm,
                        'amountquestion' => $exam->amountquestion,
                        'trials' => $exam->trials,
                        'questionmaterial' => [
                            'id' => $exam->questionmaterial->id,
                            'name' => $exam->questionmaterial->name,
                        ],
                        'class' => [
                            'id' => $exam->class->id,
                            'name' => $exam->class->name,
                        ],
                        'teacher' => [
                            'id' => $exam->teacher->id,
                            'name' => $exam->teacher->name,
                        ],
                    ]
                ]
            ], 201);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal membuat ujian.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update exam (admin version with teacher selection)
     */
    public function adminUpdate(Request $request, $id)
    {
        try {
            $exam = Exam::findOrFail($id);

            $request->validate([
                'name' => 'required|string|max:255',
                'startdate' => 'required|date',
                'enddate' => 'required|date|after_or_equal:startdate',
                'starttime' => 'required|date_format:H:i',
                'endtime' => 'required|date_format:H:i',
                'kkm' => 'required|numeric|min:0|max:100',
                'classid' => 'required|exists:class,id',
                'questionmaterialid' => 'required|exists:questionmaterial,id',
                'trials' => 'required|integer|min:1',
                'question_ids' => 'required|array|min:1',
                'question_ids.*' => 'exists:question,id',
                'teacher_id' => 'required|exists:users,id',
            ]);

            // Validate teacher exists and has teacher role
            $teacher = \App\Models\User::where('id', $request->teacher_id)
                ->where('role', 'teacher')
                ->first();
            if (!$teacher) {
                return response()->json([
                    'success' => false,
                    'message' => 'Guru tidak ditemukan atau tidak valid.'
                ], 404);
            }

            // Validate schedule conflicts (exclude current exam)
            if (Exam::hasScheduleConflict(
                $request->classid,
                $request->startdate,
                $request->enddate,
                $request->starttime,
                $request->endtime,
                $id // Exclude current exam
            )) {
                $conflictingExams = Exam::getConflictingExams(
                    $request->classid,
                    $request->startdate,
                    $request->enddate,
                    $request->starttime,
                    $request->endtime,
                    $id
                );

                $conflictDetails = $conflictingExams->map(function ($conflictExam) {
                    return [
                        'name' => $conflictExam->name,
                        'startdate' => $conflictExam->startdate,
                        'enddate' => $conflictExam->enddate,
                        'starttime' => $conflictExam->starttime,
                        'endtime' => $conflictExam->endtime,
                    ];
                });

                return response()->json([
                    'success' => false,
                    'message' => 'Jadwal ujian bentrok dengan ujian yang sudah ada untuk kelas ini.',
                    'conflicts' => $conflictDetails
                ], 422);
            }

            // Calculate duration
            $startDateTime = new \DateTime($request->startdate . ' ' . $request->starttime);
            $endDateTime = new \DateTime($request->enddate . ' ' . $request->endtime);
            $interval = $startDateTime->diff($endDateTime);
            $duration = ($interval->days * 24 * 60) + ($interval->h * 60) + $interval->i;

            if ($duration <= 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Waktu selesai harus setelah waktu mulai.'
                ], 422);
            }

            // Update exam
            $exam->update([
                'name' => $request->name,
                'startdate' => $request->startdate,
                'enddate' => $request->enddate,
                'starttime' => $request->starttime,
                'endtime' => $request->endtime,
                'duration' => $duration,
                'kkm' => $request->kkm,
                'classid' => $request->classid,
                'amountquestion' => count($request->question_ids),
                'questionmaterialid' => $request->questionmaterialid,
                'trials' => $request->trials,
                'teacher_id' => $request->teacher_id,
            ]);

            // Delete existing exam details
            ExamDetail::where('examid', $exam->id)->delete();

            // Create new exam details
            foreach ($request->question_ids as $questionId) {
                ExamDetail::create([
                    'examid' => $exam->id,
                    'questionid' => $questionId,
                    'classid' => $request->classid,
                    'studentid' => null,
                ]);
            }

            // Load relationships for response
            $exam->load(['questionmaterial', 'class', 'teacher']);

            return response()->json([
                'success' => true,
                'message' => 'Ujian berhasil diperbarui.',
                'data' => [
                    'exam' => [
                        'id' => $exam->id,
                        'name' => $exam->name,
                        'startdate' => $exam->startdate,
                        'enddate' => $exam->enddate,
                        'starttime' => $exam->starttime,
                        'endtime' => $exam->endtime,
                        'duration' => $exam->duration,
                        'token' => $exam->token,
                        'kkm' => $exam->kkm,
                        'amountquestion' => $exam->amountquestion,
                        'trials' => $exam->trials,
                        'questionmaterial' => [
                            'id' => $exam->questionmaterial->id,
                            'name' => $exam->questionmaterial->name,
                        ],
                        'class' => [
                            'id' => $exam->class->id,
                            'name' => $exam->class->name,
                        ],
                        'teacher' => [
                            'id' => $exam->teacher->id,
                            'name' => $exam->teacher->name,
                        ],
                    ]
                ]
            ], 200);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memperbarui ujian.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete exam (admin version)
     */
    public function adminDestroy($id)
    {
        try {
            $exam = Exam::findOrFail($id);

            // Delete related exam details first
            ExamDetail::where('examid', $exam->id)->delete();

            // Delete related exam results
            ExamResult::where('examid', $exam->id)->delete();

            // Delete related temporary answers
            ExamTemporaryAnswer::where('examid', $exam->id)->delete();

            // Delete the exam
            $exam->delete();

            return response()->json([
                'success' => true,
                'message' => 'Ujian berhasil dihapus.'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus ujian.',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
