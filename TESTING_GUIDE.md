# Testing Guide - HTML Support in Quill Editor

## Perubahan yang Telah Dilakukan

### Backend Changes
- Mengganti `sanitizeHtml()` dengan `cleanHtml()` di QuestionController dan QuestionApiController
- HTML dari Quill editor sekarang disimpan utuh ke database

### Website Changes
- Mengubah `{{ }}` menjadi `{!! !!}` untuk menampilkan HTML di semua komponen
- File yang diupdate: pilihan_ganda.blade.php, uraian_singkat.blade.php, esai.blade.php

### Flutter Changes
- Menambahkan plugin flutter_html
- Mengubah Text widget menjadi Html widget untuk pertanyaan dan jawaban

## Test Cases

### 1. Test Backend - Membuat Soal dengan HTML
**Langkah:**
1. Buka halaman tambah soal baru di website
2. Gunakan Quill editor untuk membuat pertanyaan dengan formatting:
   - **Bold text**
   - *Italic text*
   - Underlined text
   - Bullet points
   - Numbered lists
   - Different colors
   - Mathematical formulas (jika ada)

**Expected Result:**
- HTML tags tersimpan di database tanpa di-sanitize
- Contoh: `<p><strong>Pertanyaan</strong> ini menggunakan <em>formatting</em></p>`

### 2. Test Website - Tampilan Detail Bank Soal
**Langkah:**
1. Buka halaman detail bank soal
2. Lihat pertanyaan dan jawaban yang mengandung HTML

**Expected Result:**
- Text ditampilkan dengan formatting yang benar (bold, italic, dll)
- Tidak ada tag HTML yang terlihat di browser
- Styling tetap konsisten dengan design

### 3. Test Website - Tampilan Jadwal Ujian
**Langkah:**
1. Buka halaman jadwal ujian
2. Pilih soal untuk ujian
3. Lihat preview pertanyaan dan jawaban

**Expected Result:**
- Formatting HTML ditampilkan dengan benar
- Layout tidak rusak

### 4. Test Flutter - Detail Bank Soal
**Langkah:**
1. Buka aplikasi Flutter
2. Masuk ke halaman detail bank soal
3. Lihat pertanyaan dan jawaban

**Expected Result:**
- HTML formatting ditampilkan dengan benar menggunakan flutter_html
- Tidak ada error atau crash
- Styling konsisten dengan design app

### 5. Test Flutter - Halaman Ujian Siswa
**Langkah:**
1. Mulai ujian di aplikasi Flutter
2. Lihat pertanyaan pilihan ganda, uraian singkat, dan esai

**Expected Result:**
- Semua jenis pertanyaan menampilkan HTML dengan benar
- Jawaban pilihan ganda juga menampilkan HTML formatting
- Tidak ada performance issue

## Contoh HTML yang Harus Ditest

```html
<p><strong>Soal Pilihan Ganda:</strong></p>
<p>Manakah dari berikut ini yang <em>bukan</em> termasuk dalam <u>sistem operasi</u>?</p>
<ul>
<li>Windows</li>
<li>Linux</li>
<li>MacOS</li>
</ul>

<p style="color: red;">Perhatikan dengan baik!</p>
```

## Troubleshooting

### Jika HTML tidak ditampilkan di website:
- Pastikan menggunakan `{!! !!}` bukan `{{ }}`
- Check apakah ada XSS protection yang menghalangi

### Jika HTML tidak ditampilkan di Flutter:
- Pastikan plugin flutter_html sudah diinstall dengan `flutter pub get`
- Check import statement: `import 'package:flutter_html/flutter_html.dart';`
- Pastikan menggunakan `Html(data: ...)` bukan `Text(...)`

### Jika ada masalah styling:
- Check konfigurasi Style di Html widget
- Pastikan margin dan padding di-set ke zero jika perlu
