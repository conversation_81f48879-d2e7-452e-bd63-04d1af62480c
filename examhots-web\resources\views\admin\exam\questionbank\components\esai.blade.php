{{-- <table class="table table-hover table-striped">
    <thead>
        <tr>
            <th>No</th>
            <th>Soal</th>
            <th><PERSON><PERSON><PERSON></th>
            <th>Score</th>
            <th>Aksi</th>
        </tr>
    </thead>

    <tbody>
        @foreach ($questions as $index => $q)
            <tr>
                <td>{{ $loop->iteration }}</td>
                <td>
                    @if (!empty($q->img))
                        @foreach (explode(',', $q->img) as $img)
                            <img src="{{ asset('storage/uploads/images/question/' . trim($img)) }}" alt="Gambar Soal"
                                class="img-fluid mb-2 rounded shadow-sm me-2" style="max-width: 100px;">
                        @endforeach
                    @endif
                    <div>
                        {{ $q->question }}
                    </div>
                </td>
                <td>
                    @foreach ($q->answers as $answer)
                        <span @if ($answer->is_correct) style="color: red; font-weight: bold;" @endif>

                            {{ $answer->answer }}
                        </span>
                    @endforeach
                </td>
                <td>
                    {{ $q->answers->first()?->score ?? '-' }}
                </td>
                <td>
                    <div class="d-flex flex-wrap gap-2 mb-1">
                        <a href="{{ route('question.detail.edit.type', $q->id) }}" class="btn btn-sm btn-primary"><i
                                class="ti ti-edit"></i> Edit</a>
                        <form action="{{ route('question.detail.delete', $q->id) }}" method="POST"
                            style="display:inline;">
                            @csrf
                            @method('DELETE')
                            <button class="btn btn-sm btn-danger"><i class="ti ti-trash"></i> Hapus</button>
                        </form>
                    </div>
                </td>
            </tr>
        @endforeach
</table> --}}

@forelse ($questions as $index => $q)
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200 w-full p-6 mb-6 question-item"
        data-question="{{ strtolower($q->question) }}"
        data-answers="{{ strtolower(implode(' ', $q->answers->pluck('answer')->toArray())) }}">
        <!-- Header: Question Number, Tag dan More Options -->
        <div class="flex justify-between items-start mb-4">
            <div class="flex items-center gap-3">
                <div
                    class="flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-700 rounded-full text-sm font-semibold">
                    {{ $loop->iteration }}
                </div>
                <div class="flex items-center gap-2">
                    <span
                        class="text-sm font-medium bg-purple-50 text-purple-700 px-3 py-1 rounded-full border border-purple-200">
                        Esai
                    </span>
                    <span
                        class="text-sm font-medium bg-blue-50 text-blue-700 px-3 py-1 rounded-full border border-blue-200">
                        Skor: {{ $q->answers->first()?->score ?? '-' }}
                    </span>
                </div>
            </div>
            <div class="relative inline-block text-left">
                <button onclick="toggleDropdown(event)" aria-label="More options"
                    class="text-gray-400 hover:text-gray-600 focus:outline-none p-1 rounded-lg hover:bg-gray-50 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <circle cx="5" cy="12" r="1.5" />
                        <circle cx="12" cy="12" r="1.5" />
                        <circle cx="19" cy="12" r="1.5" />
                    </svg>
                </button>

                <div
                    class="dropdown-menu absolute right-0 mt-2 w-40 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 hidden">
                    <a href="javascript:;"
                        onclick="openEditModal(event, '{{ route('question.detail.edit.type', ['questionid' => $q->id]) }}')"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                        <iconify-icon icon="mdi:pencil" width="16" height="16"
                            class="mr-3 text-gray-400"></iconify-icon>
                        Edit Soal
                    </a>
                    <hr class="border-gray-100 my-1">
                    <form action="{{ route('question.detail.delete', $q->id) }}" method="POST" class="delete-form">
                        @csrf
                        @method('DELETE')
                        <button type="button" onclick="confirmDelete(this.form)"
                            class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                            <iconify-icon icon="mdi:delete" width="16" height="16"
                                class="mr-3 text-red-600"></iconify-icon>
                            Hapus Soal
                        </button>
                    </form>
                </div>
            </div>
        </div>



        <!-- Gambar Soal -->
        @if (!empty($q->img) && $q->img !== null && $q->img !== '')
            <div class="flex flex-wrap gap-3 mb-5">
                @foreach (explode(',', $q->img) as $img)
                    @if (trim($img) !== '')
                        <div class="relative group">
                            <img src="{{ asset('storage/uploads/images/question/' . trim($img)) }}" alt="Gambar Soal"
                                class="w-20 h-20 rounded-lg object-cover shadow-sm border border-gray-200 group-hover:shadow-md transition-shadow"
                                onerror="this.style.display='none'" />
                        </div>
                    @endif
                @endforeach
            </div>
        @endif

        <!-- Pertanyaan -->
        <div class="mb-5">
            <h3 class="text-gray-900 text-base font-medium leading-relaxed text-left">
                {!! $q->question !!}
            </h3>
        </div>

        <!-- Jawaban -->
        <div class="space-y-3">
            <div class="text-sm font-medium text-gray-600 mb-2 text-start">Jawaban:</div>
            @foreach ($q->answers as $answer)
                <div
                    class="p-4 rounded-lg border border-gray-100
                    {{ $answer->is_correct ? 'bg-green-50 border-green-200' : 'bg-gray-50' }}">
                    <div class="flex items-start space-x-2">
                        @if ($answer->is_correct)
                            <span class="text-green-600 mt-0.5">
                                <iconify-icon icon="mdi:check-circle" width="16" height="16"></iconify-icon>
                            </span>
                        @endif
                        <span
                            class="text-gray-700 text-sm leading-relaxed text-left flex-1
                            {{ $answer->is_correct ? 'font-medium text-green-800' : '' }}">
                            {!! $answer->answer !!}
                        </span>
                    </div>
                </div>
            @endforeach
        </div>

    </div>
@empty
    <div class="text-center py-2">
        <div class="mb-6">
            <svg class="w-16 h-16 text-gray-300 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                </path>
            </svg>
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Belum Ada Soal Essay</h3>
        <p class="text-gray-500 mb-6">
            Belum ada soal essay yang ditambahkan. <a href="#" class="text-primary-blue hover:underline">Ayo buat
                soal essay pertamamu sekarang!</a>
        </p>
        <div class="flex justify-center space-x-3">
            <button class="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors">
                Kembali
            </button>
            <button
                class="px-6 py-2 bg-primary-blue text-white rounded-lg hover:bg-opacity-90 font-medium transition-all duration-200 hover:shadow-lg">
                Simpan
            </button>
        </div>
    </div>
@endforelse
