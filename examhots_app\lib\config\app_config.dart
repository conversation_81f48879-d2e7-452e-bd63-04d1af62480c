class AppConfig {
  // Base URL for the API
  static const String baseUrl = 'https://hots.onvaya.web.id';

  // API endpoints
  static const String apiUrl = '$baseUrl/api';

  // Storage URLs
  static const String storageUrl = '$baseUrl/storage';
  static const String questionImageUrl = '$storageUrl/uploads/images/question';

  // Other configuration
  static const String appName = 'ExamHots';
  static const String appVersion = '1.0.0';

  // Network timeouts
  static const int connectionTimeout = 30; // seconds
  static const int receiveTimeout = 30; // seconds

  // Image configuration
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageExtensions = ['jpg', 'jpeg', 'png'];
  static const List<String> allowedFileExtensions = [
    'jpg',
    'jpeg',
    'png',
    'pdf',
    'doc',
    'docx',
  ];

  // Exam configuration
  static const int autoSaveInterval = 30; // seconds
  static const int warningTimeMinutes = 5; // minutes before exam ends

  // UI configuration
  static const double borderRadius = 8.0;
  static const double cardElevation = 2.0;

  // Colors
  static const int primaryColorValue = 0xFF455A9D;
  static const int secondaryColorValue = 0xFF31406F;
  static const int accentColorValue = 0xFF667085;

  // Helper methods
  static String getQuestionImageUrl(String imageName) {
    return '$questionImageUrl/$imageName';
  }

  static String getStorageUrl(String path) {
    return '$storageUrl/$path';
  }

  static bool isValidImageExtension(String extension) {
    return allowedImageExtensions.contains(extension.toLowerCase());
  }

  static bool isValidFileExtension(String extension) {
    return allowedFileExtensions.contains(extension.toLowerCase());
  }

  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}
