import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:dio/dio.dart';
import 'package:open_file/open_file.dart';

class ApiService {
  static const String baseUrl =
      'https://hots.onvaya.web.id/api'; // Production endpoint

  // Get stored token
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  // Store token
  static Future<void> storeToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
  }

  // Remove token
  static Future<void> removeToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
  }

  // Save login credentials for remember me
  static Future<void> saveLoginCredentials(
    String email,
    String password,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('remember_email', email);
    await prefs.setString('remember_password', password);
    await prefs.setBool('remember_me', true);
  }

  // Clear login credentials
  static Future<void> clearLoginCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('remember_email');
    await prefs.remove('remember_password');
    await prefs.setBool('remember_me', false);
  }

  // Get saved login credentials
  static Future<Map<String, dynamic>?> getSavedCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    final rememberMe = prefs.getBool('remember_me') ?? false;

    if (!rememberMe) return null;

    final email = prefs.getString('remember_email');
    final password = prefs.getString('remember_password');

    if (email != null && password != null) {
      return {'email': email, 'password': password, 'remember_me': true};
    }

    return null;
  }

  // Get headers with authorization
  static Future<Map<String, String>> getHeaders() async {
    final token = await getToken();
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  // Get headers for multipart requests
  static Future<Map<String, String>> getMultipartHeaders() async {
    final token = await getToken();
    final headers = {'Accept': 'application/json'};

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  // Login
  static Future<Map<String, dynamic>> login(
    String email,
    String password,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/auth/login'),
        headers: await getHeaders(),
        body: jsonEncode({'email': email, 'password': password}),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        // Store token
        await storeToken(data['data']['token']);
        return data;
      } else {
        // Return error response instead of throwing exception
        return {
          'success': false,
          'message':
              data['message'] ??
              'Login gagal. Periksa email dan password Anda.',
        };
      }
    } catch (e) {
      // Return network error response instead of throwing exception
      return {
        'success': false,
        'message': 'Koneksi bermasalah. Periksa koneksi internet Anda.',
      };
    }
  }

  // Logout
  static Future<Map<String, dynamic>> logout() async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/auth/logout'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        // Remove token
        await removeToken();
        return data;
      } else {
        throw Exception(data['message'] ?? 'Logout failed');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get user profile
  static Future<Map<String, dynamic>> getProfile() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/auth/profile'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get profile');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Check if user is authenticated
  static Future<bool> isAuthenticated() async {
    final token = await getToken();
    if (token == null) return false;

    try {
      await getProfile();
      return true;
    } catch (e) {
      // Token might be expired, remove it
      await removeToken();
      return false;
    }
  }

  // Update user profile
  static Future<Map<String, dynamic>> updateProfile({
    String? nip,
    required String name,
    required String email,
    required String gender,
    required String phonenumber,
    String? address,
  }) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/auth/profile'),
        headers: await getHeaders(),
        body: jsonEncode({
          'nip': nip,
          'name': name,
          'email': email,
          'gender': gender,
          'phonenumber': phonenumber,
          'address': address,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to update profile');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Change user password
  static Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
    required String newPasswordConfirmation,
  }) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/auth/change-password'),
        headers: await getHeaders(),
        body: jsonEncode({
          'current_password': currentPassword,
          'new_password': newPassword,
          'new_password_confirmation': newPasswordConfirmation,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to change password');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get all question materials
  static Future<Map<String, dynamic>> getQuestionMaterials() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/question-materials'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get question materials');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get specific question material
  static Future<Map<String, dynamic>> getQuestionMaterial(int id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/question-materials/$id'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get question material');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Create new question material
  static Future<Map<String, dynamic>> createQuestionMaterial(
    String name,
    String description, {
    int? teacherId,
  }) async {
    try {
      final body = {'name': name, 'description': description};
      if (teacherId != null) {
        body['teacher_id'] = teacherId.toString();
      }

      final response = await http.post(
        Uri.parse('$baseUrl/question-materials'),
        headers: await getHeaders(),
        body: jsonEncode(body),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 201 && data['success'] == true) {
        return data;
      } else {
        throw Exception(
          data['message'] ?? 'Failed to create question material',
        );
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get questions by material ID
  static Future<Map<String, dynamic>> getQuestionsByMaterialId(
    int materialId,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/question-materials/$materialId'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get questions');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Create new question
  static Future<Map<String, dynamic>> createQuestion({
    required int materialId,
    required String question,
    required String type,
    List<String>? imagePaths,
    List<Map<String, String>>? answers,
    int? correctAnswerIndex,
    String? answer,
    double? score,
  }) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/questions/material/$materialId'),
      );

      // Add headers for multipart
      final headers = await getMultipartHeaders();
      request.headers.addAll(headers);

      // Add form fields
      request.fields['question'] = question;
      request.fields['type'] = type;

      // Add answers based on type
      if (type == 'pilihan_ganda' &&
          answers != null &&
          correctAnswerIndex != null) {
        for (int i = 0; i < answers.length; i++) {
          request.fields['answers[$i][text]'] = answers[i]['text'] ?? '';
        }
        request.fields['correct_answer_index'] = correctAnswerIndex.toString();
      } else if ((type == 'uraian_singkat' || type == 'esai') &&
          answer != null) {
        request.fields['answer'] = answer;
        if (score != null) {
          request.fields['score'] = score.toString();
        }
      }

      // Add image files
      if (imagePaths != null && imagePaths.isNotEmpty) {
        for (int i = 0; i < imagePaths.length; i++) {
          final file = await http.MultipartFile.fromPath(
            'images[]',
            imagePaths[i],
          );
          request.files.add(file);
        }
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      final data = jsonDecode(response.body);

      if (response.statusCode == 201 && data['success'] == true) {
        return data;
      } else {
        throw Exception(
          data['message'] ??
              'Failed to create question. Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Upload single image
  static Future<Map<String, dynamic>> uploadImage(String imagePath) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/questions/upload-image'),
      );

      // Add headers
      final headers = await getHeaders();
      request.headers.addAll(headers);

      // Add image file
      final file = await http.MultipartFile.fromPath('file', imagePath);
      request.files.add(file);

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to upload image');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Upload exam answer image (same method as bank soal)
  static Future<Map<String, dynamic>> uploadAnswerImage(
    String imagePath,
  ) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/exams/upload-answer-image'),
      );

      // Add headers for multipart (same as bank soal)
      final headers = await getMultipartHeaders();
      request.headers.addAll(headers);

      // Add image file using same method as bank soal
      final file = File(imagePath);
      final bytes = await file.readAsBytes();
      final multipartFile = http.MultipartFile.fromBytes(
        'file',
        bytes,
        filename: file.path.split('/').last,
      );
      request.files.add(multipartFile);

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to upload answer image');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Upload multiple exam answer images
  static Future<Map<String, dynamic>> uploadAnswerImages(
    List<String> imagePaths,
  ) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/exams/upload-answer-images'),
      );

      // Add headers for multipart
      final headers = await getMultipartHeaders();
      request.headers.addAll(headers);

      // Add image files with array notation for Laravel
      for (int i = 0; i < imagePaths.length; i++) {
        final imagePath = imagePaths[i];
        final file = File(imagePath);
        final bytes = await file.readAsBytes();
        final multipartFile = http.MultipartFile.fromBytes(
          'files[]', // Use array notation for Laravel
          bytes,
          filename: file.path.split('/').last,
        );
        request.files.add(multipartFile);
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to upload answer images');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Create new question with XFile support (web-compatible)
  static Future<Map<String, dynamic>> createQuestionWithFiles({
    required int materialId,
    required String question,
    required String type,
    List<XFile>? imageFiles,
    List<Map<String, String>>? answers,
    int? correctAnswerIndex,
    String? answer,
    double? score,
  }) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/questions/material/$materialId'),
      );

      // Add headers for multipart
      final headers = await getMultipartHeaders();
      request.headers.addAll(headers);

      // Add form fields
      request.fields['question'] = question;
      request.fields['type'] = type;

      // Add answers based on type
      if (type == 'pilihan_ganda' &&
          answers != null &&
          correctAnswerIndex != null) {
        for (int i = 0; i < answers.length; i++) {
          request.fields['answers[$i][text]'] = answers[i]['text'] ?? '';
        }
        request.fields['correct_answer_index'] = correctAnswerIndex.toString();
      } else if ((type == 'uraian_singkat' || type == 'esai') &&
          answer != null) {
        request.fields['answer'] = answer;
        if (score != null) {
          request.fields['score'] = score.toString();
        }
      }

      // Add image files (web-compatible)
      if (imageFiles != null && imageFiles.isNotEmpty) {
        for (int i = 0; i < imageFiles.length; i++) {
          final bytes = await imageFiles[i].readAsBytes();
          final file = http.MultipartFile.fromBytes(
            'images[]', // Use array notation for multiple files
            bytes,
            filename: imageFiles[i].name,
          );
          request.files.add(file);
        }
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      final data = jsonDecode(response.body);

      if (response.statusCode == 201 && data['success'] == true) {
        return data;
      } else {
        throw Exception(
          data['message'] ??
              'Failed to create question. Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Delete question
  static Future<Map<String, dynamic>> deleteQuestion(int questionId) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/questions/$questionId'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to delete question');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Update question material
  static Future<Map<String, dynamic>> updateQuestionMaterial(
    int id,
    String name,
    String description, {
    int? teacherId,
  }) async {
    try {
      final body = {'name': name, 'description': description};
      if (teacherId != null) {
        body['teacher_id'] = teacherId.toString();
      }

      final response = await http.put(
        Uri.parse('$baseUrl/question-materials/$id'),
        headers: await getHeaders(),
        body: jsonEncode(body),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(
          data['message'] ?? 'Failed to update question material',
        );
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Update question material scores
  static Future<Map<String, dynamic>> updateQuestionMaterialScores(
    int materialId,
    double? pgTotalScore,
    double? uraianTotalScore,
  ) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/question-materials/$materialId/scores'),
        headers: await getHeaders(),
        body: jsonEncode({
          'pg_total_score': pgTotalScore?.toInt(),
          'uraian_total_score': uraianTotalScore?.toInt(),
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to update scores');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Check exam time status
  static Future<Map<String, dynamic>> checkExamTime(int examId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/exams/$examId/check-time'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to check exam time');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Validate exam token
  static Future<Map<String, dynamic>> validateExamToken(
    String token,
    int examId,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/exams/validate-token'),
        headers: await getHeaders(),
        body: jsonEncode({'token': token, 'exam_id': examId}),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        // Handle specific error cases
        if (response.statusCode == 400 || response.statusCode == 404) {
          throw Exception(data['message'] ?? 'Token validation failed');
        } else if (response.statusCode == 422 && data['errors'] != null) {
          Map<String, dynamic> errors = data['errors'];
          List<String> errorMessages = [];
          errors.forEach((field, messages) {
            if (messages is List) {
              errorMessages.addAll(messages.cast<String>());
            } else {
              errorMessages.add(messages.toString());
            }
          });
          throw Exception(errorMessages.join(', '));
        } else {
          throw Exception(data['message'] ?? 'Token validation failed');
        }
      }
    } catch (e) {
      if (e.toString().contains('Token ujian tidak valid') ||
          e.toString().contains('Anda sudah menyelesaikan ujian ini') ||
          e.toString().contains('Ujian belum dimulai') ||
          e.toString().contains('Waktu ujian sudah berakhir') ||
          e.toString().contains('Data tidak valid')) {
        rethrow; // Re-throw specific errors as-is
      }
      throw Exception(e);
    }
  }

  // Get exam result
  static Future<Map<String, dynamic>> getExamResult(int examId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/exams/$examId/result'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get exam result');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Save temporary answer
  static Future<Map<String, dynamic>> saveTemporaryAnswer({
    required int examId,
    required int questionId,
    required String questionType,
    String? selectedAnswer,
    String? textAnswer,
    String? imagePath, // Keep for backward compatibility
    List<String>? imagePaths, // New parameter for multiple images
    bool? hasAttachment,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/exams/save-temporary-answer'),
        headers: await getHeaders(),
        body: jsonEncode({
          'exam_id': examId,
          'question_id': questionId,
          'question_type': questionType,
          'selected_answer': selectedAnswer,
          'text_answer': textAnswer,
          'image_path': imagePath, // Keep for backward compatibility
          'image_paths': imagePaths, // New field for multiple images
          'has_attachment': hasAttachment,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to save temporary answer');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get temporary answers
  static Future<Map<String, dynamic>> getTemporaryAnswers(int examId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/exams/$examId/temporary-answers'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get temporary answers');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get exam questions (randomized for students)
  static Future<Map<String, dynamic>> getExamQuestions(int examId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/exams/$examId/questions'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get exam questions');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get exam participants (for teachers)
  static Future<Map<String, dynamic>> getExamParticipants(int examId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/exams/$examId/participants'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get exam participants');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get student exam detail (for teachers)
  static Future<Map<String, dynamic>> getStudentExamDetail(
    int examId,
    int studentId,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/exams/$examId/student/$studentId/detail'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get student exam detail');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Update student essay score (for teachers)
  static Future<Map<String, dynamic>> updateStudentEssayScore(
    int examId,
    int studentId,
    int questionId,
    double score,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/exams/$examId/student/$studentId/essay-score'),
        headers: await getHeaders(),
        body: jsonEncode({'question_id': questionId, 'score': score}),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to update essay score');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Submit exam answers
  static Future<Map<String, dynamic>> submitExamAnswers(
    int examId,
    List<Map<String, dynamic>> answers,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/exams/$examId/submit'),
        headers: await getHeaders(),
        body: jsonEncode({
          'answers': answers,
          'submitted_at': DateTime.now().toIso8601String(),
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        // Handle validation errors specifically
        if (response.statusCode == 422 && data['errors'] != null) {
          Map<String, dynamic> errors = data['errors'];
          List<String> errorMessages = [];
          errors.forEach((field, messages) {
            if (messages is List) {
              errorMessages.addAll(messages.cast<String>());
            } else {
              errorMessages.add(messages.toString());
            }
          });
          throw Exception(errorMessages.join(', '));
        } else {
          throw Exception(data['message'] ?? 'Failed to submit exam');
        }
      }
    } catch (e) {
      if (e.toString().contains('Data tidak valid')) {
        rethrow; // Re-throw validation errors as-is
      }
      throw Exception(e);
    }
  }

  // Delete question material
  static Future<Map<String, dynamic>> deleteQuestionMaterial(int id) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/question-materials/$id'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(
          data['message'] ?? 'Failed to delete question material',
        );
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Update question
  static Future<Map<String, dynamic>> updateQuestion({
    required int questionId,
    required String question,
    required String type,
    List<String>? imagePaths,
    List<Map<String, String>>? answers,
    int? correctAnswerIndex,
    String? answer,
  }) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/questions/$questionId/update'),
      );

      // Add headers
      final headers = await getMultipartHeaders();
      request.headers.addAll(headers);

      // Add form fields
      request.fields['question'] = question;
      request.fields['type'] = type;

      // Add answers based on question type
      if (type == 'pilihan_ganda' && answers != null) {
        for (int i = 0; i < answers.length; i++) {
          request.fields['answers[$i][text]'] = answers[i]['text'] ?? '';
        }
        if (correctAnswerIndex != null) {
          request.fields['correct_answer_index'] =
              correctAnswerIndex.toString();
        }
      } else if ((type == 'uraian_singkat' || type == 'esai') &&
          answer != null) {
        request.fields['answer'] = answer;
      }

      // Add image files
      if (imagePaths != null && imagePaths.isNotEmpty) {
        for (int i = 0; i < imagePaths.length; i++) {
          final file = await http.MultipartFile.fromPath(
            'images[]',
            imagePaths[i],
          );
          request.files.add(file);
        }
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to update question');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Update question with XFile support (web-compatible)
  static Future<Map<String, dynamic>> updateQuestionWithFiles({
    required int questionId,
    required String question,
    required String type,
    List<XFile>? imageFiles,
    String? existingImages,
    List<Map<String, String>>? answers,
    int? correctAnswerIndex,
    String? answer,
    double? score,
  }) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/questions/$questionId/update'),
      );

      // Add headers
      final headers = await getMultipartHeaders();
      request.headers.addAll(headers);

      // Add form fields
      request.fields['question'] = question;
      request.fields['type'] = type;

      // Add answers based on question type
      if (type == 'pilihan_ganda' && answers != null) {
        for (int i = 0; i < answers.length; i++) {
          request.fields['answers[$i][text]'] = answers[i]['text'] ?? '';
        }
        if (correctAnswerIndex != null) {
          request.fields['correct_answer_index'] =
              correctAnswerIndex.toString();
        }
      } else if ((type == 'uraian_singkat' || type == 'esai') &&
          answer != null) {
        request.fields['answer'] = answer;
        if (type == 'esai' && score != null) {
          request.fields['score'] = score.toString();
        }
      }

      // Add existing images
      if (existingImages != null && existingImages.isNotEmpty) {
        request.fields['existing_images'] = existingImages;
      }

      // Add image files (web-compatible)
      if (imageFiles != null && imageFiles.isNotEmpty) {
        for (int i = 0; i < imageFiles.length; i++) {
          final bytes = await imageFiles[i].readAsBytes();
          final file = http.MultipartFile.fromBytes(
            'images[]', // Use array notation for multiple files
            bytes,
            filename: imageFiles[i].name,
          );
          request.files.add(file);
        }
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to update question');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // ===== TEACHER MANAGEMENT METHODS =====

  // Get all teachers
  static Future<Map<String, dynamic>> getTeachers() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/teachers'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get teachers');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get teacher by ID
  static Future<Map<String, dynamic>> getTeacher(int id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/teachers/$id'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get teacher');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Create new teacher
  static Future<Map<String, dynamic>> createTeacher({
    required String nip,
    required String name,
    required String email,
    required String password,
    required String gender,
    required String phonenumber,
    String? address,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/teachers'),
        headers: await getHeaders(),
        body: jsonEncode({
          'nip': nip,
          'name': name,
          'email': email,
          'password': password,
          'gender': gender,
          'phonenumber': phonenumber,
          'address': address,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 201 && data['success'] == true) {
        return data;
      } else if (response.statusCode == 422) {
        // Handle validation errors
        String errorMessage = 'Terjadi kesalahan validasi';
        if (data['errors'] != null) {
          final errors = data['errors'] as Map<String, dynamic>;
          final errorMessages = <String>[];

          errors.forEach((field, messages) {
            if (messages is List) {
              errorMessages.addAll(messages.cast<String>());
            } else if (messages is String) {
              errorMessages.add(messages);
            }
          });

          if (errorMessages.isNotEmpty) {
            errorMessage = errorMessages.join(', ');
          }
        } else if (data['message'] != null) {
          errorMessage = data['message'];
        }

        throw Exception(errorMessage);
      } else {
        throw Exception(data['message'] ?? 'Failed to create teacher');
      }
    } catch (e) {
      if (e.toString().contains('Exception:')) {
        rethrow;
      }
      throw Exception(e);
    }
  }

  // Update teacher
  static Future<Map<String, dynamic>> updateTeacher({
    required int id,
    required String nip,
    required String name,
    required String email,
    String? password,
    required String gender,
    required String phonenumber,
    String? address,
  }) async {
    try {
      final body = {
        'nip': nip,
        'name': name,
        'email': email,
        'gender': gender,
        'phonenumber': phonenumber,
        'address': address,
      };

      // Only include password if provided
      if (password != null && password.isNotEmpty) {
        body['password'] = password;
      }

      final response = await http.put(
        Uri.parse('$baseUrl/teachers/$id'),
        headers: await getHeaders(),
        body: jsonEncode(body),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to update teacher');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Delete teacher
  static Future<Map<String, dynamic>> deleteTeacher(int id) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/teachers/$id'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to delete teacher');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // ===== CLASS MANAGEMENT METHODS =====

  // Get all classes
  static Future<Map<String, dynamic>> getClasses() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/classes'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get classes');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get class by ID
  static Future<Map<String, dynamic>> getClass(int id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/classes/$id'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get class');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Create new class
  static Future<Map<String, dynamic>> createClass({
    required String name,
    int? teacherId,
    String? level,
  }) async {
    try {
      final body = {'name': name, 'level': level};

      if (teacherId != null) {
        body['teacherid'] = teacherId.toString();
      }

      final response = await http.post(
        Uri.parse('$baseUrl/classes'),
        headers: await getHeaders(),
        body: jsonEncode(body),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 201 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to create class');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Update class
  static Future<Map<String, dynamic>> updateClass({
    required int id,
    required String name,
    int? teacherId,
    String? level,
  }) async {
    try {
      final body = {'name': name, 'level': level};

      if (teacherId != null) {
        body['teacherid'] = teacherId.toString();
      }

      final response = await http.put(
        Uri.parse('$baseUrl/classes/$id'),
        headers: await getHeaders(),
        body: jsonEncode(body),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to update class');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Delete class
  static Future<Map<String, dynamic>> deleteClass(int id) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/classes/$id'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to delete class');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get teachers for dropdown
  static Future<Map<String, dynamic>> getTeachersForClass() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/classes/teachers'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get teachers');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // ===== STUDENT MANAGEMENT METHODS =====

  // Get all students
  static Future<Map<String, dynamic>> getStudents() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/students'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get students');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get student by ID
  static Future<Map<String, dynamic>> getStudent(int id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/students/$id'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get student');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Create new student
  static Future<Map<String, dynamic>> createStudent({
    required String nisn,
    required String nis,
    required String name,
    required String parentname,
    required String gender,
    required String phonenumber,
    String? religion,
    String? address,
    required int classid,
    required String email,
    String? password,
  }) async {
    try {
      final body = {
        'nisn': nisn,
        'nis': nis,
        'name': name,
        'parentname': parentname,
        'gender': gender,
        'phonenumber': phonenumber,
        'religion': religion,
        'address': address,
        'classid': classid.toString(),
        'email': email,
      };

      if (password != null && password.isNotEmpty) {
        body['password'] = password;
      }

      final response = await http.post(
        Uri.parse('$baseUrl/students'),
        headers: await getHeaders(),
        body: jsonEncode(body),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 201 && data['success'] == true) {
        return data;
      } else if (response.statusCode == 422) {
        // Handle validation errors
        String errorMessage = 'Terjadi kesalahan validasi';
        if (data['errors'] != null) {
          final errors = data['errors'] as Map<String, dynamic>;
          final errorMessages = <String>[];

          errors.forEach((field, messages) {
            if (messages is List) {
              errorMessages.addAll(messages.cast<String>());
            } else if (messages is String) {
              errorMessages.add(messages);
            }
          });

          if (errorMessages.isNotEmpty) {
            errorMessage = errorMessages.join(', ');
          }
        } else if (data['message'] != null) {
          errorMessage = data['message'];
        }

        throw Exception(errorMessage);
      } else {
        throw Exception(data['message'] ?? 'Failed to create student');
      }
    } catch (e) {
      if (e.toString().contains('Exception:')) {
        rethrow;
      }
      throw Exception(e);
    }
  }

  // Update student
  static Future<Map<String, dynamic>> updateStudent({
    required int id,
    required String nisn,
    required String nis,
    required String name,
    required String parentname,
    required String gender,
    required String phonenumber,
    String? religion,
    String? address,
    required int classid,
    required String email,
    String? password,
  }) async {
    try {
      final body = {
        'nisn': nisn,
        'nis': nis,
        'name': name,
        'parentname': parentname,
        'gender': gender,
        'phonenumber': phonenumber,
        'religion': religion,
        'address': address,
        'classid': classid.toString(),
        'email': email,
      };

      if (password != null && password.isNotEmpty) {
        body['password'] = password;
      }

      final response = await http.put(
        Uri.parse('$baseUrl/students/$id'),
        headers: await getHeaders(),
        body: jsonEncode(body),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else if (response.statusCode == 422) {
        // Handle validation errors
        String errorMessage = 'Terjadi kesalahan validasi';
        if (data['errors'] != null) {
          final errors = data['errors'] as Map<String, dynamic>;
          final errorMessages = <String>[];

          errors.forEach((field, messages) {
            if (messages is List) {
              errorMessages.addAll(messages.cast<String>());
            } else if (messages is String) {
              errorMessages.add(messages);
            }
          });

          if (errorMessages.isNotEmpty) {
            errorMessage = errorMessages.join(', ');
          }
        } else if (data['message'] != null) {
          errorMessage = data['message'];
        }

        throw Exception(errorMessage);
      } else {
        throw Exception(data['message'] ?? 'Failed to update student');
      }
    } catch (e) {
      if (e.toString().contains('Exception:')) {
        rethrow;
      }
      throw Exception(e);
    }
  }

  // Delete student
  static Future<Map<String, dynamic>> deleteStudent(int id) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/students/$id'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to delete student');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get classes for student dropdown
  static Future<Map<String, dynamic>> getClassesForStudent() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/students/classes'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get classes');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // ===== ADMIN MANAGEMENT METHODS =====

  // Get all admins
  static Future<Map<String, dynamic>> getAdmins() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/admins'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get admins');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get admin by ID
  static Future<Map<String, dynamic>> getAdmin(int id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/admins/$id'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get admin');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Create new admin
  static Future<Map<String, dynamic>> createAdmin({
    required String name,
    required String email,
    required String password,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/admins'),
        headers: await getHeaders(),
        body: jsonEncode({'name': name, 'email': email, 'password': password}),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 201 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to create admin');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Update admin
  static Future<Map<String, dynamic>> updateAdmin({
    required int id,
    required String name,
    required String email,
    String? password,
  }) async {
    try {
      final body = {'name': name, 'email': email};

      // Only include password if provided
      if (password != null && password.isNotEmpty) {
        body['password'] = password;
      }

      final response = await http.put(
        Uri.parse('$baseUrl/admins/$id'),
        headers: await getHeaders(),
        body: jsonEncode(body),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to update admin');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Delete admin
  static Future<Map<String, dynamic>> deleteAdmin(int id) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/admins/$id'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to delete admin');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // ===== EXAM MANAGEMENT METHODS =====

  // Get all exams
  static Future<Map<String, dynamic>> getExams() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/exams'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get exams');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get exam by ID
  static Future<Map<String, dynamic>> getExam(int id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/exams/$id'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get exam');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get form data for exam creation
  static Future<Map<String, dynamic>> getExamFormData() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/exams/form-data'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get form data');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get questions by material ID
  static Future<Map<String, dynamic>> getQuestionsByMaterial(
    int materialId,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/exams/questions/$materialId'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get questions');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Create new exam
  static Future<Map<String, dynamic>> createExam({
    required String name,
    required String startdate,
    required String enddate,
    required String starttime,
    required String endtime,
    required int duration,
    required String kkm,
    required int classid,
    required int questionmaterialid,
    required int trials,
    required List<int> questionIds,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/exams'),
        headers: await getHeaders(),
        body: jsonEncode({
          'name': name,
          'startdate': startdate,
          'enddate': enddate,
          'starttime': starttime,
          'endtime': endtime,
          'duration': duration,
          'kkm': kkm,
          'classid': classid,
          'questionmaterialid': questionmaterialid,
          'trials': trials,
          'question_ids': questionIds,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 201 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to create exam');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Update exam
  static Future<Map<String, dynamic>> updateExam({
    required int id,
    required String name,
    required String startdate,
    required String enddate,
    required String starttime,
    required String endtime,
    required int duration,
    required String kkm,
    required int classid,
    required int questionmaterialid,
    required int trials,
    required List<int> questionIds,
  }) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/exams/$id'),
        headers: await getHeaders(),
        body: jsonEncode({
          'name': name,
          'startdate': startdate,
          'enddate': enddate,
          'starttime': starttime,
          'endtime': endtime,
          'duration': duration,
          'kkm': kkm,
          'classid': classid,
          'questionmaterialid': questionmaterialid,
          'trials': trials,
          'question_ids': questionIds,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to update exam');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Delete exam
  static Future<Map<String, dynamic>> deleteExam(int id) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/exams/$id'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to delete exam');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Get student exams
  static Future<Map<String, dynamic>> getStudentExams() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/student/exams'),
        headers: await getHeaders(),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get student exams');
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // Export exam participants to Excel
  static Future<String> exportExamToExcel(int examId) async {
    try {
      final token = await getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      // Try direct download first
      try {
        return await downloadExamFile(examId, 'excel');
      } catch (downloadError) {
        // If direct download fails, try opening in browser
        final webBaseUrl = baseUrl.replaceAll('/api', '');
        final url = '$webBaseUrl/export/$examId/excel?token=$token';
        final Uri downloadUri = Uri.parse(url);

        if (await canLaunchUrl(downloadUri)) {
          await launchUrl(
            downloadUri,
            mode: LaunchMode.externalApplication,
            webOnlyWindowName: '_blank',
          );
          return 'File Excel dibuka di browser untuk diunduh';
        } else {
          // Last resort: provide the URL to user
          return 'Download gagal. Silakan buka URL ini di browser: $url';
        }
      }
    } catch (e) {
      throw Exception('Failed to export Excel: $e');
    }
  }

  // Export exam participants to PDF
  static Future<String> exportExamToPdf(int examId) async {
    try {
      final token = await getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      // Try direct download first
      try {
        return await downloadExamFile(examId, 'pdf');
      } catch (downloadError) {
        // If direct download fails, try opening in browser
        final webBaseUrl = baseUrl.replaceAll('/api', '');
        final url = '$webBaseUrl/export/$examId/pdf?token=$token';
        final Uri downloadUri = Uri.parse(url);

        if (await canLaunchUrl(downloadUri)) {
          await launchUrl(
            downloadUri,
            mode: LaunchMode.externalApplication,
            webOnlyWindowName: '_blank',
          );
          return 'File PDF dibuka di browser untuk diunduh';
        } else {
          // Last resort: provide the URL to user
          return 'Download gagal. Silakan buka URL ini di browser: $url';
        }
      }
    } catch (e) {
      throw Exception('Failed to export PDF: $e');
    }
  }

  // Alternative method using Dio for direct file download
  static Future<String> downloadExamFile(int examId, String fileType) async {
    try {
      final token = await getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      // For Android, use app-specific directory which doesn't require permission
      // This is more reliable than requesting storage permission

      final dio = Dio();
      final url = '$baseUrl/exams/$examId/export/$fileType';

      // Get app-specific directory (no permission required)
      final directory = await getApplicationDocumentsDirectory();
      final downloadsPath = '${directory.path}/Downloads';
      await Directory(downloadsPath).create(recursive: true);

      final fileExtension = fileType == 'excel' ? 'xlsx' : 'pdf';
      final fileName =
          'Laporan_Ujian_${examId}_${DateTime.now().millisecondsSinceEpoch}.$fileExtension';
      final filePath = '$downloadsPath/$fileName';

      await dio.download(
        url,
        filePath,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Accept':
                fileType == 'excel'
                    ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    : 'application/pdf',
          },
          responseType: ResponseType.bytes,
        ),
      );

      // Try to open the downloaded file automatically
      try {
        final result = await OpenFile.open(filePath);
        if (result.type == ResultType.done) {
          return 'File berhasil diunduh dan dibuka: $fileName';
        } else if (result.type == ResultType.noAppToOpen) {
          return 'File berhasil diunduh: $fileName\nTidak ada aplikasi untuk membuka file ini.\nSilakan install aplikasi PDF/Excel viewer.';
        } else if (result.type == ResultType.permissionDenied) {
          return 'File berhasil diunduh: $fileName\nPermission ditolak untuk membuka file.\nSilakan buka manual dari: $downloadsPath';
        } else {
          return 'File berhasil diunduh: $fileName\nLokasi: $downloadsPath\nSilakan buka manual dari file manager';
        }
      } catch (openError) {
        return 'File berhasil diunduh: $fileName\nLokasi: $downloadsPath\nSilakan buka manual dari file manager';
      }
    } catch (e) {
      throw Exception('Failed to download file: $e');
    }
  }
}
